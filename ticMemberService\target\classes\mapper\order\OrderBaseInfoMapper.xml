<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper">
    <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderBaseInfo">
        <id column="ORDER_ID" property="orderId" jdbcType="BIGINT"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="QUESTION_ID" property="questionId" jdbcType="BIGINT"/>
        <result column="LAB_ID" property="labId" jdbcType="BIGINT"/>
        <result column="LAB_NAME" property="labName" jdbcType="VARCHAR"/>
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL"/>
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="PAY_DATE" property="payDate" jdbcType="TIMESTAMP"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP"/>
        <result column="PLATFORM" property="platform" jdbcType="VARCHAR"/>
        <result column="PLATFORM_ORDER" property="platformOrder" jdbcType="VARCHAR"/>
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR"/>
        <result column="CS_BRANCH" property="csBranch" jdbcType="VARCHAR"/>
        <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL"/>
        <result column="OFFER_DATE" property="offerDate" jdbcType="TIMESTAMP"/>
        <result column="ORDER_EXP_DATE" property="orderExpDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR"/>
        <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL"/>
        <result column="IS_URGENT" property="isUrgent" jdbcType="TINYINT"/>
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR"/>
        <result column="BU" property="bu" jdbcType="VARCHAR"/>
        <result column="USER_SEX" property="userSex" jdbcType="TINYINT"/>
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR"/>
        <result column="IS_READ" property="isRead" jdbcType="TINYINT"/>
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR"/>
        <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR"/>
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR"/>
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER"/>
        <result column="CS_NAME" property="csName" jdbcType="VARCHAR"/>
        <result column="LINE_ID" property="lineId" jdbcType="BIGINT"/>
        <result column="APPLICATION_LINE_ID" property="applicationLineId" jdbcType="BIGINT"/>
        <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="TMP_GROUP_NO" property="tmpGroupNo" jdbcType="VARCHAR"/>
        <result column="IS_REMIND" property="isRemind" jdbcType="TINYINT"/>
        <result column="CS_NAME_EN" property="csNameEn" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="CATAGORY_ID" property="catagoryId" jdbcType="BIGINT"/>
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER"/>
        <result column="TOTAL_NUMS" property="totalNums" jdbcType="INTEGER"/>
        <result column="PRODUCT_IMG" property="productImg" jdbcType="VARCHAR"/>
        <result column="IS_TEST" property="isTest" jdbcType="TINYINT"/>
        <result column="IS_INVOICE" property="isInvoice" jdbcType="TINYINT"/>
        <result column="AUDIT_CODE" property="auditCode" jdbcType="VARCHAR"/>
        <result column="REPORT_LUA_CODE" property="reportLuaCode" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM_CODE" property="reportFormCode" jdbcType="VARCHAR"/>
        <result column="IS_DELETE" property="isDelete" jdbcType="TINYINT"/>
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="TINYINT"/>
        <result column="PRO_STATE" property="proState" jdbcType="INTEGER"/>
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
    </resultMap>


    <resultMap id="RSTSEXPResultMap" type="com.sgs.ecom.member.dto.export.ExpRSTSOrderDTO">
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT"/>
        <result column="RELATE_ORDER_NO" property="mainOrderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR"/>
        <result column="CONFIRM_ORDER_DATE" property="confirmOrderDate" jdbcType="VARCHAR"/>
        <result column="STATE" property="orderState" jdbcType="VARCHAR"/>
        <result column="MONTH_PAY" property="billingType" jdbcType="VARCHAR"/>
        <result column="PAY_STATE" property="payState" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME_CN" property="companyName" jdbcType="VARCHAR"/>
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR"/>
        <result column="REAL_AMOUNT" property="orderAmount" jdbcType="VARCHAR"/>
        <result column="LAB_NAME" property="sendSampleArea" jdbcType="VARCHAR"/>
        <result column="LINK_PERSON" property="formPerson" jdbcType="VARCHAR"/>
        <result column="LINK_PHONE" property="formPhone" jdbcType="VARCHAR" />
        <result column="LINK_EMAIL" property="formMail" jdbcType="VARCHAR" />

    </resultMap>
    <resultMap id="RSTSPersonCenterResultMap" type="com.sgs.ecom.member.dto.rsts.RstsPersonCenterDTO">
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="STATE_COUNT" property="stateCount" jdbcType="INTEGER"/>
        <result column="ORDER_NO_STR" property="orderNoStr" jdbcType="VARCHAR"/>
    </resultMap>


    <resultMap id="BaseResultInfo" type="com.sgs.ecom.member.entity.order.OrderBaseInfo">
        <id column="ORDER_ID" property="orderId" jdbcType="BIGINT"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR"/>
        <result column="TMP_GROUP_NO" property="tmpGroupNo" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER"/>
        <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER"/>
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER"/>
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR"/>
        <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR"/>
        <result column="COMPANY_ADDRESS_EN" property="companyAddressEn" jdbcType="VARCHAR"/>
        <result column="TOWN" property="town" jdbcType="VARCHAR"/>
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR"/>
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR"/>
        <result column="CS_NAME" property="csName" jdbcType="VARCHAR"/>
        <result column="CS_NAME_EN" property="csNameEn" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER"/>
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL"/>
        <result column="BU" property="bu" jdbcType="VARCHAR"/>
        <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR"/>
        <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR"/>
        <result column="LINE_ID" property="lineId" jdbcType="INTEGER"/>
        <result column="APPLICATION_LINE_ID" property="applicationLineId" jdbcType="BIGINT"/>
        <result column="LAB_ID" property="labId" jdbcType="INTEGER"/>
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="TINYINT"/>
        <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER"/>
        <result column="CONFIRM_ORDER_DATE" property="confirmOrderDate" jdbcType="TIMESTAMP"/>
        <result column="SALES_CODE" property="salesCode" jdbcType="VARCHAR"/>
        <result column="SALES_PHONE" property="salesPhone" jdbcType="VARCHAR"/>
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER"/>
        <result column="CATEGORY_ID" property="catagoryId" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="INTEGER"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="USER_SEX" property="userSex" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="AUDIT_CODE" property="auditCode" jdbcType="VARCHAR"/>
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="TOTAL_NUMS" property="totalNums" jdbcType="INTEGER"/>
        <result column="PRODUCT_IMG" property="productImg" jdbcType="VARCHAR"/>
        <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="IS_TEST" property="isTest" jdbcType="INTEGER"/>
        <result column="IS_INVOICE" property="isInvoice" jdbcType="INTEGER"/>
        <result column="LAB_NAME" property="labName" jdbcType="VARCHAR"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR"/>
        <result column="IS_READ" property="isRead" jdbcType="VARCHAR"/>
        <result column="OFFER_DATE" property="offerDate" jdbcType="TIMESTAMP"/>
        <result column="ORDER_EXP_DATE" property="orderExpDate" jdbcType="TIMESTAMP"/>
        <result column="SHOP_DIS_AMOUNT" property="shopDisAmount" jdbcType="DECIMAL"/>
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL"/>
        <result column="CS_DISCOUNT_AMOUNT" property="csDiscountAmount" jdbcType="DECIMAL"/>
        <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR"/>
        <result column="RECOMMEND_REASON_IMAGE" property="recommendReasonImage" jdbcType="VARCHAR"/>
        <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR"/>
        <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL"/>
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="TINYINT"/>
        <result column="QUESTION_ID" property="questionId" jdbcType="VARCHAR"/>
        <result column="TEST_CYCLE_MEMO" property="testCycleMemo" jdbcType="VARCHAR"/>
        <result column="LEADS_CODE" property="leadsCode" jdbcType="VARCHAR"/>
        <result column="ORDER_SOURCE" property="orderSource" jdbcType="VARCHAR"/>
        <result column="IS_URGENT" property="isUrgent" jdbcType="INTEGER"/>
        <result column="TEST_LABEL" property="testLabel" jdbcType="INTEGER"/>
        <result column="CATAGORY_ID" property="catagoryId" jdbcType="INTEGER"/>
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER"/>
        <result column="DEADLINE_TIME" property="deadlineTime" jdbcType="VARCHAR"/>
        <result column="ORDER_SOURCE_FROM" property="orderSourceFrom" jdbcType="VARCHAR"/>
        <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER"/>
        <result column="CONFIRM_ORDER_DATE" property="confirmOrderDate" jdbcType="VARCHAR"/>
        <result column="SALES_CODE" property="salesCode" jdbcType="VARCHAR"/>
        <result column="SALES_PHONE" property="salesPhone" jdbcType="VARCHAR"/>
        <result column="PROMO_INFO" property="promoInfo" jdbcType="VARCHAR"/>
        <result column="FROM_SOURCE" property="fromSource" jdbcType="VARCHAR"/>
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR"/>
        <result column="EXCHANGE_RATE" property="exchangeRate" jdbcType="DECIMAL"/>
        <result column="PLATFORM_AMOUNT" property="platformAmount" jdbcType="DECIMAL"/>
        <result column="APPLY_SUBMIT_DATE" property="applySubmitDate" jdbcType="VARCHAR"/>
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
        <result column="ABSTRACT_CUSTCODE" property="abstractCustcode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ORDER_ID
        , ORDER_NO, ORDER_TYPE, ORDER_AMOUNT, QUESTION_ID, LAB_ID, LAB_NAME, DISCOUNT_AMOUNT,
    REAL_AMOUNT, STATE, PAY_STATE, USER_ID, USER_PHONE, USER_NAME, USER_EMAIL, COMPANY_NAME,
    PROVINCE, CITY, RELATE_ORDER_NO, PAY_DATE, CREATE_DATE, STATE_DATE, PLATFORM, PLATFORM_ORDER,
    CS_CODE, CS_BRANCH, SERVICE_AMOUNT, OFFER_DATE, ORDER_EXP_DATE, REPORT_LUA, REPORT_FORM,
    TEST_CYCLE, IS_URGENT, CATEGORY_PATH, BU, USER_SEX, CS_EMAIL, IS_READ, RECOMMEND_REASON,
    SAMPLE_REQUIREMENTS, GROUP_NO, HIS_STATE, CS_NAME, LINE_ID, APPLICATION_LINE_ID, BUSINESS_LINE, URGENT_AMOUNT,
    TMP_GROUP_NO, IS_REMIND, CS_NAME_EN, PRODUCT_NAME, CATAGORY_ID, SUB_STATE, TOTAL_NUMS,
    PRODUCT_IMG, IS_TEST, IS_INVOICE, AUDIT_CODE, REPORT_LUA_CODE, REPORT_FORM_CODE,
    IS_DELETE, IS_PAY_RECEIVED,PRO_STATE,IS_ELECTRON, REFUND_STATE
    </sql>
    <select id="selectVOByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from ORDER_BASE_INFO
        where ORDER_ID = #{orderId,jdbcType=BIGINT}
    </select>

    <insert id="insertVOSelective" parameterType="com.sgs.ecom.member.vo.VOOrderBaseInfo">
        <selectKey resultType="java.lang.Long" keyProperty="orderId" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into ORDER_BASE_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                ORDER_ID,
            </if>
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="orderType != null">
                ORDER_TYPE,
            </if>
            <if test="orderAmount != null">
                ORDER_AMOUNT,
            </if>
            <if test="shopDisAmount != null">
                SHOP_DIS_AMOUNT,
            </if>
            <if test="questionId != null">
                QUESTION_ID,
            </if>
            <if test="labId != null">
                LAB_ID,
            </if>
            <if test="labName != null">
                LAB_NAME,
            </if>
            <if test="discountAmount != null">
                DISCOUNT_AMOUNT,
            </if>
            <if test="realAmount != null">
                REAL_AMOUNT,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="proState != null">
                PRO_STATE,
            </if>
            <if test="payState != null">
                PAY_STATE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="userPhone != null">
                USER_PHONE,
            </if>
            <if test="userName != null">
                USER_NAME,
            </if>
            <if test="userEmail != null">
                USER_EMAIL,
            </if>
            <if test="companyName != null">
                COMPANY_NAME,
            </if>
            <if test="province != null">
                PROVINCE,
            </if>
            <if test="city != null">
                CITY,
            </if>
            <if test="relateOrderNo != null">
                RELATE_ORDER_NO,
            </if>
            <if test="payDate != null">
                PAY_DATE,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="stateDate != null">
                STATE_DATE,
            </if>
            <if test="platform != null">
                PLATFORM,
            </if>
            <if test="platformOrder != null">
                PLATFORM_ORDER,
            </if>
            <if test="csCode != null">
                CS_CODE,
            </if>
            <if test="csBranch != null">
                CS_BRANCH,
            </if>
            <if test="serviceAmount != null">
                SERVICE_AMOUNT,
            </if>
            <if test="offerDate != null">
                OFFER_DATE,
            </if>
            <if test="orderExpDate != null">
                ORDER_EXP_DATE,
            </if>
            <if test="reportLua != null">
                REPORT_LUA,
            </if>
            <if test="reportForm != null">
                REPORT_FORM,
            </if>
            <if test="testCycle != null">
                TEST_CYCLE,
            </if>
            <if test="isUrgent != null">
                IS_URGENT,
            </if>
            <if test="categoryPath != null">
                CATEGORY_PATH,
            </if>
            <if test="bu != null">
                BU,
            </if>
            <if test="userSex != null">
                USER_SEX,
            </if>
            <if test="csEmail != null">
                CS_EMAIL,
            </if>
            <if test="isRead != null">
                IS_READ,
            </if>
            <if test="recommendReason != null">
                RECOMMEND_REASON,
            </if>
            <if test="sampleRequirements != null">
                SAMPLE_REQUIREMENTS,
            </if>
            <if test="groupNo != null">
                GROUP_NO,
            </if>
            <if test="hisState != null">
                HIS_STATE,
            </if>
            <if test="csName != null">
                CS_NAME,
            </if>
            <if test="lineId != null">
                LINE_ID,
            </if>
            <if test="applicationLineId != null">
                APPLICATION_LINE_ID,
            </if>
            <if test="businessLine != null">
                BUSINESS_LINE,
            </if>
            <if test="urgentAmount != null">
                URGENT_AMOUNT,
            </if>
            <if test="tmpGroupNo != null">
                TMP_GROUP_NO,
            </if>
            <if test="isRemind != null">
                IS_REMIND,
            </if>
            <if test="csNameEn != null">
                CS_NAME_EN,
            </if>
            <if test="productName != null">
                PRODUCT_NAME,
            </if>
            <if test="catagoryId != null">
                CATAGORY_ID,
            </if>
            <if test="subState != null">
                SUB_STATE,
            </if>
            <if test="totalNums != null">
                TOTAL_NUMS,
            </if>
            <if test="productImg != null">
                PRODUCT_IMG,
            </if>
            <if test="isTest != null">
                IS_TEST,
            </if>
            <if test="isInvoice != null">
                IS_INVOICE,
            </if>
            <if test="auditCode != null">
                AUDIT_CODE,
            </if>
            <if test="reportLuaCode != null">
                REPORT_LUA_CODE,
            </if>
            <if test="reportFormCode != null">
                REPORT_FORM_CODE,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="isPayReceived != null">
                IS_PAY_RECEIVED,
            </if>
            <if test="testLabel != null">
                TEST_LABEL,
            </if>
            <if test="lastResponseDate != null">
                LAST_RESPONSE_DATE,
            </if>
            <if test="orderSource != null">
                ORDER_SOURCE,
            </if>
            <if test="orderSourceFrom != null">
                ORDER_SOURCE_FROM,
            </if>
            <if test="confirmOrderDate != null">
                CONFIRM_ORDER_DATE,
            </if>
            <if test="promoInfo != null">
                PROMO_INFO,
            </if>
            <if test="salesCode != null">
                SALES_CODE,
            </if>
            <if test="salesPhone != null">
                SALES_PHONE,
            </if>
            <if test="fromSource != null">
                FROM_SOURCE,
            </if>
            <if test="fromUrl != null">
                FROM_URL,
            </if>
            <if test="bossNo != null">
                BOSS_NO,
            </if>
            <if test="custId != null">
                CUST_ID,
            </if>
            <if test="monthPay != null">
                MONTH_PAY,
            </if>
            <if test="currency != null">
                CURRENCY,
            </if>
            <if test="companyNameEn != null">
                COMPANY_NAME_EN,
            </if>
            <if test="companyAddressCn != null">
                COMPANY_ADDRESS_CN,
            </if>
            <if test="companyAddressEn != null">
                COMPANY_ADDRESS_EN,
            </if>
            <if test="town != null">
                TOWN,
            </if>
            <if test="isElectron != null">
                IS_ELECTRON,
            </if>
            <if test="createCode != null">
                CREATE_CODE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="shopDisAmount != null">
                #{shopDisAmount,jdbcType=DECIMAL},
            </if>
            <if test="questionId != null">
                #{questionId,jdbcType=BIGINT},
            </if>
            <if test="labId != null">
                #{labId,jdbcType=BIGINT},
            </if>
            <if test="labName != null">
                #{labName,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                #{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="proState != null">
                #{proState,jdbcType=INTEGER},
            </if>
            <if test="payState != null">
                #{payState,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userPhone != null">
                #{userPhone,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userEmail != null">
                #{userEmail,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="relateOrderNo != null">
                #{relateOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payDate != null">
                #{payDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="platformOrder != null">
                #{platformOrder,jdbcType=VARCHAR},
            </if>
            <if test="csCode != null">
                #{csCode,jdbcType=VARCHAR},
            </if>
            <if test="csBranch != null">
                #{csBranch,jdbcType=VARCHAR},
            </if>
            <if test="serviceAmount != null">
                #{serviceAmount,jdbcType=DECIMAL},
            </if>
            <if test="offerDate != null">
                #{offerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderExpDate != null">
                #{orderExpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportLua != null">
                #{reportLua,jdbcType=VARCHAR},
            </if>
            <if test="reportForm != null">
                #{reportForm,jdbcType=VARCHAR},
            </if>
            <if test="testCycle != null">
                #{testCycle,jdbcType=DECIMAL},
            </if>
            <if test="isUrgent != null">
                #{isUrgent,jdbcType=TINYINT},
            </if>
            <if test="categoryPath != null">
                #{categoryPath,jdbcType=VARCHAR},
            </if>
            <if test="bu != null">
                #{bu,jdbcType=VARCHAR},
            </if>
            <if test="userSex != null">
                #{userSex,jdbcType=TINYINT},
            </if>
            <if test="csEmail != null">
                #{csEmail,jdbcType=VARCHAR},
            </if>
            <if test="isRead != null">
                #{isRead,jdbcType=TINYINT},
            </if>
            <if test="recommendReason != null">
                #{recommendReason,jdbcType=VARCHAR},
            </if>
            <if test="sampleRequirements != null">
                #{sampleRequirements,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                #{groupNo,jdbcType=VARCHAR},
            </if>
            <if test="hisState != null">
                #{hisState,jdbcType=INTEGER},
            </if>
            <if test="csName != null">
                #{csName,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                #{lineId,jdbcType=BIGINT},
            </if>
            <if test="applicationLineId != null">
                #{applicationLineId,jdbcType=BIGINT},
            </if>
            <if test="businessLine != null">
                #{businessLine,jdbcType=VARCHAR},
            </if>
            <if test="urgentAmount != null">
                #{urgentAmount,jdbcType=DECIMAL},
            </if>
            <if test="tmpGroupNo != null">
                #{tmpGroupNo,jdbcType=VARCHAR},
            </if>
            <if test="isRemind != null">
                #{isRemind,jdbcType=TINYINT},
            </if>
            <if test="csNameEn != null">
                #{csNameEn,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="catagoryId != null">
                #{catagoryId,jdbcType=BIGINT},
            </if>
            <if test="subState != null">
                #{subState,jdbcType=INTEGER},
            </if>
            <if test="totalNums != null">
                #{totalNums,jdbcType=INTEGER},
            </if>
            <if test="productImg != null">
                #{productImg,jdbcType=VARCHAR},
            </if>
            <if test="isTest != null">
                #{isTest,jdbcType=TINYINT},
            </if>
            <if test="isInvoice != null">
                #{isInvoice,jdbcType=TINYINT},
            </if>
            <if test="auditCode != null">
                #{auditCode,jdbcType=VARCHAR},
            </if>
            <if test="reportLuaCode != null">
                #{reportLuaCode,jdbcType=VARCHAR},
            </if>
            <if test="reportFormCode != null">
                #{reportFormCode,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="isPayReceived != null">
                #{isPayReceived,jdbcType=TINYINT},
            </if>
            <if test="testLabel != null">
                #{testLabel},
            </if>
            <if test="lastResponseDate != null">
                #{lastResponseDate},
            </if>
            <if test="orderSource != null">
                #{orderSource},
            </if>
            <if test="orderSourceFrom != null">
                #{orderSourceFrom},
            </if>
            <if test="confirmOrderDate != null">
                #{confirmOrderDate},
            </if>
            <if test="promoInfo != null">
                #{promoInfo},
            </if>
            <if test="salesCode != null">
                #{salesCode},
            </if>
            <if test="salesPhone != null">
                #{salesPhone},
            </if>
            <if test="fromSource != null">
                #{fromSource,jdbcType=VARCHAR},
            </if>
            <if test="fromUrl != null">
                #{fromUrl,jdbcType=VARCHAR},
            </if>
            <if test="bossNo != null">
                #{bossNo},
            </if>
            <if test="custId != null">
                #{custId},
            </if>
            <if test="monthPay != null">
                #{monthPay},
            </if>
            <if test="currency != null">
                #{currency},
            </if>
            <if test="companyNameEn != null">
                #{companyNameEn,jdbcType=VARCHAR},
            </if>
            <if test="companyAddressCn != null">
                #{companyAddressCn,jdbcType=VARCHAR},
            </if>
            <if test="companyAddressEn != null">
                #{companyAddressEn,jdbcType=VARCHAR},
            </if>
            <if test="town != null">
                #{town,jdbcType=VARCHAR},
            </if>
            <if test="isElectron != null">
                #{isElectron,jdbcType=TINYINT},
            </if>
            <if test="createCode != null">
                #{createCode},
            </if>
        </trim>
    </insert>

    <update id="updateVOByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderBaseInfo">
        update ORDER_BASE_INFO
        <set>
            <if test="orderNo != null">
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                ORDER_TYPE = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="questionId != null">
                QUESTION_ID = #{questionId,jdbcType=BIGINT},
            </if>
            <if test="labId != null">
                LAB_ID = #{labId,jdbcType=BIGINT},
            </if>
            <if test="labName != null">
                LAB_NAME = #{labName,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                DISCOUNT_AMOUNT = #{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=INTEGER},
            </if>
            <if test="proState != null">
                PRO_STATE = #{proState,jdbcType=INTEGER},
            </if>
            <if test="payState != null">
                PAY_STATE = #{payState,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userPhone != null">
                USER_PHONE = #{userPhone,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                USER_NAME = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userEmail != null">
                USER_EMAIL = #{userEmail,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                PROVINCE = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                CITY = #{city,jdbcType=VARCHAR},
            </if>
            <if test="relateOrderNo != null">
                RELATE_ORDER_NO = #{relateOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payDate != null">
                PAY_DATE = #{payDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="platform != null">
                PLATFORM = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="platformOrder != null">
                PLATFORM_ORDER = #{platformOrder,jdbcType=VARCHAR},
            </if>
            <if test="csCode != null">
                CS_CODE = #{csCode,jdbcType=VARCHAR},
            </if>
            <if test="csBranch != null">
                CS_BRANCH = #{csBranch,jdbcType=VARCHAR},
            </if>
            <if test="serviceAmount != null">
                SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
            </if>
            <if test="offerDate != null">
                OFFER_DATE = #{offerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderExpDate != null">
                ORDER_EXP_DATE = #{orderExpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportLua != null">
                REPORT_LUA = #{reportLua,jdbcType=VARCHAR},
            </if>
            <if test="reportForm != null">
                REPORT_FORM = #{reportForm,jdbcType=VARCHAR},
            </if>
            <if test="testCycle != null">
                TEST_CYCLE = #{testCycle,jdbcType=DECIMAL},
            </if>
            <if test="isUrgent != null">
                IS_URGENT = #{isUrgent,jdbcType=TINYINT},
            </if>
            <if test="categoryPath != null">
                CATEGORY_PATH = #{categoryPath,jdbcType=VARCHAR},
            </if>
            <if test="bu != null">
                BU = #{bu,jdbcType=VARCHAR},
            </if>
            <if test="userSex != null">
                USER_SEX = #{userSex,jdbcType=TINYINT},
            </if>
            <if test="csEmail != null">
                CS_EMAIL = #{csEmail,jdbcType=VARCHAR},
            </if>
            <if test="isRead != null">
                IS_READ = #{isRead,jdbcType=TINYINT},
            </if>
            <if test="recommendReason != null">
                RECOMMEND_REASON = #{recommendReason,jdbcType=VARCHAR},
            </if>
            <if test="sampleRequirements != null">
                SAMPLE_REQUIREMENTS = #{sampleRequirements,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                GROUP_NO = #{groupNo,jdbcType=VARCHAR},
            </if>
            <if test="hisState != null">
                HIS_STATE = #{hisState,jdbcType=INTEGER},
            </if>
            <if test="csName != null">
                CS_NAME = #{csName,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                LINE_ID = #{lineId,jdbcType=BIGINT},
            </if>
            <if test="applicationLineId != null">
                APPLICATION_LINE_ID = #{applicationLineId,jdbcType=BIGINT},
            </if>
            <if test="businessLine != null">
                BUSINESS_LINE = #{businessLine,jdbcType=VARCHAR},
            </if>
            <if test="urgentAmount != null">
                URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
            </if>
            <if test="tmpGroupNo != null">
                TMP_GROUP_NO = #{tmpGroupNo,jdbcType=VARCHAR},
            </if>
            <if test="isRemind != null">
                IS_REMIND = #{isRemind,jdbcType=TINYINT},
            </if>
            <if test="csNameEn != null">
                CS_NAME_EN = #{csNameEn,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="catagoryId != null">
                CATAGORY_ID = #{catagoryId,jdbcType=BIGINT},
            </if>
            <if test="subState != null">
                SUB_STATE = #{subState,jdbcType=INTEGER},
            </if>
            <if test="refundState != null">
                REFUND_STATE = #{refundState,jdbcType=INTEGER},
            </if>
            <if test="totalNums != null">
                TOTAL_NUMS = #{totalNums,jdbcType=INTEGER},
            </if>
            <if test="productImg != null">
                PRODUCT_IMG = #{productImg,jdbcType=VARCHAR},
            </if>
            <if test="abstractCustcode != null">
                ABSTRACT_CUSTCODE = #{abstractCustcode,jdbcType=VARCHAR},
            </if>
            <if test="isTest != null">
                IS_TEST = #{isTest,jdbcType=TINYINT},
            </if>
            <if test="isInvoice != null">
                IS_INVOICE = #{isInvoice,jdbcType=TINYINT},
            </if>
            <if test="auditCode != null">
                AUDIT_CODE = #{auditCode,jdbcType=VARCHAR},
            </if>
            <if test="reportLuaCode != null">
                REPORT_LUA_CODE = #{reportLuaCode,jdbcType=VARCHAR},
            </if>
            <if test="reportFormCode != null">
                REPORT_FORM_CODE = #{reportFormCode,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="isPayReceived != null">
                IS_PAY_RECEIVED = #{isPayReceived,jdbcType=TINYINT},
            </if>
            <if test="accountNo != null">
                ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="payMethod != null">
                PAY_METHOD = #{payMethod},
            </if>
            <if test="operatorSource != null">
                OPERATOR_SOURCE = #{operatorSource},
            </if>
            <if test="confirmOrderDate != null">
                CONFIRM_ORDER_DATE = #{confirmOrderDate},
            </if>
            <if test="promoInfo != null">
                PROMO_INFO= #{promoInfo},
            </if>
            <if test="salesCode != null">
                SALES_CODE=#{salesCode},
            </if>
            <if test="salesPhone != null">
                SALES_PHONE= #{salesPhone},
            </if>
            <if test="bossNo != null">
                BOSS_NO= #{bossNo},
            </if>
            <if test="custId != null">
                CUST_ID= #{custId},
            </if>
            <if test="monthPay != null">
                MONTH_PAY= #{monthPay},
            </if>
            <if test="currency != null">
                CURRENCY= #{currency},
            </if>
            <if test="userPhone != null">
                USER_PHONE= #{userPhone},
            </if>
            <if test="userName != null">
                USER_NAME= #{userName},
            </if>
            <if test="userEmail != null">
                USER_EMAIL= #{userEmail},
            </if>
            <if test="companyName != null">
                COMPANY_NAME= #{companyName},
            </if>
            <if test="province != null">
                PROVINCE= #{province},
            </if>
            <if test="city != null">
                CITY= #{city},
            </if>
            <if test="companyNameEn != null">
                COMPANY_NAME_EN= #{companyNameEn},
            </if>
            <if test="companyAddressCn != null">
                COMPANY_ADDRESS_CN= #{companyAddressCn},
            </if>
            <if test="companyAddressEn != null">
                COMPANY_ADDRESS_EN= #{companyAddressEn},
            </if>
            <if test="town != null">
                TOWN= #{town},
            </if>
            <if test="isElectron != null">
                IS_ELECTRON= #{isElectron},
            </if>
        </set>
        where ORDER_ID = #{orderId,jdbcType=BIGINT}
        <if test="confirmUseState">
            AND STATE=#{confirmUseState}
        </if>
    </update>


    <!--自定义-->


    <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.OrderBaseInfoDTO"
               extends="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper.baseMap">

        <result column="LAB_NAME" property="labName" jdbcType="VARCHAR"/>
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="INTEGER"/>
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="INTEGER"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR"/>
        <result column="IS_READ" property="isRead" jdbcType="INTEGER"/>
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER"/>
        <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>

        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER"/>
        <result column="TOTAL_NUMS" property="totalNums" jdbcType="INTEGER"/>
        <result column="PRODUCT_IMG" property="productImg" jdbcType="VARCHAR"/>
        <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR"/>
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER"/>
        <result column="PRO_STATE" property="proState" jdbcType="INTEGER"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="IS_DELETE" property="isDelete" jdbcType="TINYINT"/>
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="TINYINT"/>
        <result column="IS_INVOICE" property="isInvoice" jdbcType="INTEGER"/>
        <result column="DEADLINE_TIME" property="deadlineTime" jdbcType="TIMESTAMP"/>
        <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER"/>
        <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER"/>
        <result column="PLATFORM_AMOUNT" property="platformAmount" jdbcType="DECIMAL"/>
        <result column="CONFIRM_ORDER_DATE" property="confirmOrderDate" jdbcType="TIMESTAMP"/>
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER"/>
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR"/>
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR"/>
        <result column="APPLY_SUBMIT_DATE" property="applySubmitDate" jdbcType="VARCHAR"/>
        <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="SHOP_DIS_AMOUNT" property="shopDisAmount" jdbcType="DECIMAL"/>
        <result column="CS_DISCOUNT_AMOUNT" property="csDiscountAmount" jdbcType="DECIMAL"/>
        <result column="ITEM_ID" property="itemId" jdbcType="INTEGER" />
        <result column="SALE_NUM" property="saleNum" jdbcType="INTEGER" />
        <result column="ITEM_TYPE" property="itemType" jdbcType="INTEGER" />
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR"/>
        <result column="out_order_no" property="outOrderNo" jdbcType="VARCHAR"/>
        <result column="PLATFORM_ORDER" property="platformOrder" jdbcType="VARCHAR"/>
    </resultMap>


    <resultMap id="resultMoreDTO" type="com.sgs.ecom.member.dto.OrderBaseInfoMoreDTO"
               extends="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper.baseMap">


        <result column="LAB_NAME" property="labNameValue" jdbcType="VARCHAR"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP"/>
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR"/>
        <result column="IS_READ" property="isRead" jdbcType="VARCHAR"/>
        <result column="OFFER_DATE" property="offerDate" jdbcType="TIMESTAMP"/>
        <result column="ORDER_EXP_DATE" property="orderExpDate" jdbcType="TIMESTAMP"/>
        <result column="USER_SEX" property="userSex" jdbcType="INTEGER"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER"/>
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR"/>
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL"/>
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL"/>
        <result column="CS_DISCOUNT_AMOUNT" property="csDiscountAmount" jdbcType="DECIMAL"/>
        <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER"/>

        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="IS_URGENT" property="isUrgent" jdbcType="TINYINT"/>
        <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR"/>
        <result column="REPORT_LUA" property="reportLuaValue" jdbcType="VARCHAR"/>
        <result column="REPORT_LUA_CODE" property="reportLuaCodeValue" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM" property="reportFormValue" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM_CODE" property="reportFormCodeValue" jdbcType="VARCHAR"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR"/>
        <result column="RECOMMEND_REASON_IMAGE" property="recommendReasonImage" jdbcType="VARCHAR"/>
        <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR"/>
        <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL"/>
        <result column="TEST_CYCLE_MEMO" property="testCycleMemo" jdbcType="VARCHAR"/>
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER"/>
        <result column="TOTAL_NUMS" property="totalNums" jdbcType="INTEGER"/>
        <result column="PRODUCT_IMG" property="productImg" jdbcType="VARCHAR"/>
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER"/>
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER"/>
        <result column="PRO_STATE" property="proState" jdbcType="INTEGER"/>
        <result column="IS_DELETE" property="isDelete" jdbcType="TINYINT"/>
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="TINYINT"/>
        <result column="IS_INVOICE" property="isInvoice" jdbcType="INTEGER"/>
        <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR"/>
        <result column="APPLY_SUBMIT_DATE" property="applySubmitDate" jdbcType="TIMESTAMP"/>
        <result column="CONFIRM_ORDER_DATE" property="confirmOrderDate" jdbcType="TIMESTAMP"/>
        <result column="LAST_RESPONSE_DATE" property="timeDateShow" jdbcType="VARCHAR"/>
        <result column="DEADLINE_TIME" property="deadlineTime" jdbcType="TIMESTAMP"/>
        <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER"/>
        <result column="CLOSE_REASON" property="closeReason" jdbcType="VARCHAR"/>
        <result column="PLATFORM_AMOUNT" property="platformAmount" jdbcType="DECIMAL"/>
        <result column="SHOP_DIS_AMOUNT" property="shopDisAmount" jdbcType="DECIMAL"/>
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR"/>
        <result column="EXCHANGE_RATE" property="exchangeRate" jdbcType="DECIMAL"/>
        <result column="CS_PHONE" property="csPhone" jdbcType="VARCHAR"/>
        <result column="CS_NAME" property="csName" jdbcType="VARCHAR"/>
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
        <result column="TAX_RATES" property="taxRates" jdbcType="DECIMAL"/>
    </resultMap>


    <sql id="sqlListDTO">
        <include refid="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper.baseSelectSql"/>,
        CREATE_DATE,LAB_NAME,DISCOUNT_AMOUNT,ORDER_AMOUNT,
        CATEGORY_PATH,IS_READ,BUSINESS_LINE,HIS_STATE,
        PRODUCT_NAME,RELATE_ORDER_NO,TOTAL_NUMS,PRODUCT_IMG,SUB_STATE,REPORT_LUA,REPORT_FORM,PAY_STATE,MONTH_PAY,
        IS_DELETE,IS_PAY_RECEIVED,IS_INVOICE,DEADLINE_TIME,PAY_METHOD,REFUND_STATE,PLATFORM_AMOUNT,
        CONFIRM_ORDER_DATE,PRO_STATE,CURRENCY,APPLY_SUBMIT_DATE,URGENT_AMOUNT,SERVICE_AMOUNT,
        EXCHANGE_RATE,SHOP_DIS_AMOUNT,CS_DISCOUNT_AMOUNT,RECOMMEND_REASON,out_order_no,PLATFORM_ORDER
    </sql>

    <sql id="BaseQrySql">
        ORDER_ID
        ,ORDER_NO,STATE,USER_ID,GROUP_NO,BU,ORDER_TYPE,LAB_ID,CS_CODE,REAL_AMOUNT,
       PROMO_INFO,SALES_CODE,SALES_PHONE,BOSS_NO,CUST_ID,COMPANY_NAME_EN,
COMPANY_ADDRESS_CN,COMPANY_ADDRESS_EN,TOWN,COMPANY_NAME,PROVINCE,CITY,USER_PHONE,USER_NAME,USER_EMAIL,IS_ELECTRON,
        CREATE_DATE,LAB_NAME,DISCOUNT_AMOUNT,ORDER_AMOUNT,
        CATEGORY_PATH,IS_READ,BUSINESS_LINE,HIS_STATE,
        PRODUCT_NAME,RELATE_ORDER_NO,TOTAL_NUMS,PRODUCT_IMG,SUB_STATE,REPORT_LUA,REPORT_FORM,PAY_STATE,MONTH_PAY,
        IS_DELETE,IS_PAY_RECEIVED,IS_INVOICE,DEADLINE_TIME,PAY_METHOD,REFUND_STATE,PLATFORM_AMOUNT,
        CONFIRM_ORDER_DATE,PRO_STATE,CURRENCY,APPLY_SUBMIT_DATE,URGENT_AMOUNT,SERVICE_AMOUNT,EXCHANGE_RATE,SHOP_DIS_AMOUNT,CS_DISCOUNT_AMOUNT
    </sql>


    <select id="selectDTOByOrderNo" resultMap="resultMoreDTO">
        select
        <include refid="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper.baseSelectSql"/>,
        CREATE_DATE,LAB_NAME,SHOP_DIS_AMOUNT,
        CATEGORY_PATH,IS_READ,OFFER_DATE,ORDER_EXP_DATE,
        USER_PHONE,USER_NAME,USER_EMAIL,USER_SEX,COMPANY_NAME,PROVINCE,CITY,
        CS_CODE,CS_NAME,CS_EMAIL,HIS_STATE,BUSINESS_LINE,
        DISCOUNT_AMOUNT,SERVICE_AMOUNT,ORDER_AMOUNT,IS_PAY_RECEIVED,
        URGENT_AMOUNT,IS_URGENT,STATE_DATE,
        RECOMMEND_REASON,RECOMMEND_REASON_IMAGE,SAMPLE_REQUIREMENTS,TEST_CYCLE,TEST_CYCLE_MEMO,
        PRODUCT_NAME,RELATE_ORDER_NO,PAY_STATE,MONTH_PAY,
        IS_DELETE,IS_PAY_RECEIVED,SUB_STATE,IS_INVOICE,
        REPORT_LUA, REPORT_FORM,REPORT_LUA_CODE,REPORT_FORM_CODE,OPERATOR_CODE,APPLY_SUBMIT_DATE,
        LAST_RESPONSE_DATE,DEADLINE_TIME,PAY_METHOD,REFUND_STATE,CLOSE_REASON,PLATFORM_AMOUNT,CONFIRM_ORDER_DATE,PRO_STATE,
        CURRENCY,CS_PHONE,CS_DISCOUNT_AMOUNT,TAX_RATES
        from ORDER_BASE_INFO
        where ORDER_NO = #{orderNo}
    </select>
    <select id="selectListByMap" resultMap="resultDTO">
        select
        <include refid="sqlListDTO"/>
        from ORDER_BASE_INFO obi
        <include refid="baseQueryWhere"/>
        <include refid="orderBy"/>
        <include refid="useLimit"/>
    </select>
    <select id="qryRelateOrderByOrder" resultMap="BaseResultInfo" parameterType="com.sgs.ecom.member.vo.VOOrderBaseInfo">
        select
        <include refid="Base_Column_List"/>
        from ORDER_BASE_INFO obi
        where RELATE_ORDER_NO = #{relateOrderNo, jdbcType=VARCHAR}
        and ORDER_TYPE = #{orderType, jdbcType=INTEGER}
    </select>
    <select id="selectListCountByMap" resultType="java.lang.Integer">
        select count(1)
        from ORDER_BASE_INFO obi
        <include refid="baseQueryWhere"/>
    </select>


    <sql id="baseQueryWhere">
        where 1=1
        <if test="orderId != null">
            AND obi.ORDER_ID=#{orderId}
        </if>
        <if test="relateOrderNo != null">
            AND obi.RELATE_ORDER_NO=#{relateOrderNo}
        </if>
        <if test="userId != null">
            AND obi.USER_ID=#{userId}
        </if>
        <if test="orderType != null">
            AND obi.ORDER_TYPE=#{orderType}
        </if>
        <if test="orderTypeList != null ">
            AND obi.ORDER_TYPE in
            <foreach collection="orderTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="sampleStr != null ">
            AND obi.ORDER_NO in(
            select obi.ORDER_NO
            from order_sample_from osf ,order_sample os ,order_base_info obi
            where osf.ORDER_NO=obi.ORDER_NO and osf.GROUP_NO=obi.GROUP_NO
            and osf.SAMPLE_NO=os.SAMPLE_NO
            and os.ORDER_NO=obi.ORDER_NO and os.GROUP_NO=obi.GROUP_NO
            and os.STATE=1
            and obi.ORDER_TYPE in (210000,210001)
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            and (os.SAMPLE_NAME_CN like concat('%',#{sampleStr},'%')  or os.SAMPLE_NAME_EN like  concat('%',#{sampleStr},'%')
             or osf.SAMPLE_VALUE like concat('%',#{sampleStr},'%')
             )
            group by obi.ORDER_NO
            )
        </if>
        <if test="itemStr != null ">
            AND obi.ORDER_NO in(
            select obi.ORDER_NO from  order_detail od ,order_base_info obi
            where od.ORDER_NO=obi.ORDER_NO and od.GROUP_NO=obi.GROUP_NO
            and od.ITEM_NAME like concat('%',#{itemStr},'%')
            and obi.ORDER_TYPE in (210000,210001)
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            group by obi.ORDER_NO)
        </if>



        <if test="state != null">
            AND obi.STATE=#{state}
        </if>
        <if test="stateNot != null">
            AND obi.STATE!=#{stateNot}
        </if>
        <if test="isDelete != null">
            AND obi.IS_DELETE=#{isDelete}
        </if>
        <if test="subState != null">
            AND obi.SUB_STATE=#{subState}
        </if>
        <if test="subStateNot != null">
            AND obi.SUB_STATE!=#{subStateNot}
        </if>
        <if test="payState != null">
            AND obi.PAY_STATE=#{payState}
        </if>
        <if test="roPayState != null">
            <if test="roPayState==0">
                and (obi.PAY_METHOD is null or obi.ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO
                where ORDER_TYPE=301000 and PAY_METHOD is null) )
            </if>
            <if test="roPayState==1">
                and (obi.PAY_METHOD is not null or obi.ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO
                where ORDER_TYPE=301000 and PAY_METHOD is not null) )
            </if>
        </if>
        <if test="proState != null">
            AND obi.PAY_STATE=#{proState}
        </if>
        <if test="monthPay != null and orderType == 100000" >
            <if test="monthPay ==0">
                AND obi.MONTH_PAY=0
            </if>
            <if test="monthPay ==2">
                AND obi.MONTH_PAY in(1,3)
            </if>
        </if>
        <if test="labId != null">
            AND obi.LAB_ID=#{labId}
        </if>
        <if test="bu != null">
            AND obi.BU=#{bu}
        </if>
        <if test="invoiceNumber != null">
            AND obi.ORDER_NO in (SELECT ORDER_NO FROM ORDER_ATTACHMENT where INVOICE_NUMBER=#{invoiceNumber}
             and state=1 and att_Type=11 )
        </if>

        <if test="downReport != null">
            <if test="downReport == 0">
                AND obi.ORDER_NO not in(
                select DISTINCT ORDER_NO from ORDER_ATTACHMENT where
                obi.ORDER_NO not in(
                select DISTINCT ORDER_NO from ORDER_ATTACHMENT where ATT_TYPE=10 and DOWN_LOAD=0
                ) and ATT_TYPE=10
                )
            </if>
            <if test="downReport == 1">
                AND obi.ORDER_NO in(
                select DISTINCT ORDER_NO from ORDER_ATTACHMENT where
                ORDER_NO not in(
                select DISTINCT ORDER_NO from ORDER_ATTACHMENT where ATT_TYPE=10 and DOWN_LOAD=0
                ) and ATT_TYPE=10
                )
            </if>
        </if>
        <if test="stateList != null ">
            AND
            <foreach collection="stateList" index="index" item="item" open="(" separator=" or " close=")">

                <if test="item == 53">
                    obi.ORDER_NO IN (
                    SELECT DISTINCT (CASE WHEN obi.RELATE_ORDER_NO is not null then obi.RELATE_ORDER_NO else 	obi.ORDER_NO END) as ORDER_NO from order_base_info obi where

                    obi.ORDER_TYPE IN (100000,101000)
                    AND
                    obi.REFUND_STATE=1)
                </if>
                <if test="item == 55">
                    obi.ORDER_NO IN (
                    SELECT DISTINCT (CASE WHEN obi.RELATE_ORDER_NO is not null then obi.RELATE_ORDER_NO else 	obi.ORDER_NO END) as ORDER_NO from order_base_info obi where
                    obi.ORDER_TYPE IN (100000,101000)
                    AND
                    obi.REFUND_STATE =4)
                </if>
                <if test="item == 56">
                    obi.REFUND_STATE = 5
                    or
                    obi.ORDER_NO  in (SELECT DISTINCT (CASE WHEN obi.RELATE_ORDER_NO is not null then obi.RELATE_ORDER_NO else 	obi.ORDER_NO END) as ORDER_NO from order_base_info obi, order_operator_log ool where obi.ORDER_NO = ool.ORDER_NO and  obi.ORDER_TYPE IN (100000,101000)  and  ool.OPERATOR_TYPE in (744,2744))
                </if>

                <if test=" item != 53   and item != 55 and item != 56 ">
                    obi.STATE=#{item}
                </if>
            </foreach>
        </if>

        <if test="statesListOr != null  ">
            and (obi.SUB_STATE=70 or
            <foreach collection="statesListOr" index="index" item="item"  separator=" or  ">
            <if test="item == 53">
                  obi.ORDER_NO IN (
                SELECT DISTINCT (CASE WHEN obi.RELATE_ORDER_NO is not null then obi.RELATE_ORDER_NO else 	obi.ORDER_NO END) as ORDER_NO from order_base_info obi where

                obi.ORDER_TYPE IN (100000,101000)
                AND
                obi.REFUND_STATE=1)
            </if>
            <if test="item == 55">
                obi.ORDER_NO IN (
                SELECT DISTINCT (CASE WHEN obi.RELATE_ORDER_NO is not null then obi.RELATE_ORDER_NO else 	obi.ORDER_NO END) as ORDER_NO from order_base_info obi where
                obi.ORDER_TYPE IN (100000,101000)
                AND
                obi.REFUND_STATE =4)
            </if>
            <if test="item == 56">
                  obi.REFUND_STATE = 5
                or
                obi.ORDER_NO  in (SELECT DISTINCT (CASE WHEN obi.RELATE_ORDER_NO is not null then obi.RELATE_ORDER_NO else 	obi.ORDER_NO END) as ORDER_NO from order_base_info obi, order_operator_log ool where obi.ORDER_NO = ool.ORDER_NO and  obi.ORDER_TYPE IN (100000,101000)  and  ool.OPERATOR_TYPE in (744,2744))

            </if>
            <if test=" item != 53   and item != 55 and item != 56 ">
                obi.STATE = #{item}
            </if>
            </foreach>
            )
        </if>


        <if test="custIdList != null">
            and obi.CUST_ID in
            <foreach collection="custIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="orderNoList != null ">
            AND ORDER_NO in
            <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="(statesListOr != null  and statesListOr.contains(12)  and !statesListOr.contains(13)) or (stateList != null  and stateList.contains(12) and  !stateList.contains(13))">
            AND not exists
            (select 1 from

            (

            SELECT
            distinct obi.order_id
            FROM
            order_base_info obi,
            order_express oe
            WHERE
            obi.order_no = oe.order_no
            AND oe.DELIVER_TYPE = 10
            AND obi.STATE = 12
            AND oe.IS_CS = 1
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            <if test="orderType != null">
                AND obi.ORDER_TYPE=#{orderType}
            </if>

            <if test="isDelete != null">
                AND obi.IS_DELETE =#{isDelete}
            </if>


            UNION ALL
            select distinct obi.order_id from order_base_info obi,order_product op where
            op.ORDER_NO =  obi.ORDER_NO  and op.state = 1
            AND obi.STATE = 12
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            <if test="orderType != null">
                AND obi.ORDER_TYPE=#{orderType}
            </if>
            <if test="isDelete != null">
                AND obi.IS_DELETE =#{isDelete}
            </if>
            AND (op.STORE_ID in ('INSP','CBE','EHS')  or op.SUB_BU_CODE in ('SL-PX','RSTS-SDS')))w where w.order_id=obi.order_id)
        </if>
        <if test="keywordOrderNo != null">
            <![CDATA[
          AND (
          obi.ORDER_NO like concat('%',#{keywordOrderNo},'%') or
          obi.ORDER_NO in (select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=211000 and ORDER_NO like concat('%',#{keywordOrderNo},'%'))
        )
         ]]>
        </if>

        <if test="keyword != null">
            <if test="orderType == 200000">
                <![CDATA[
        AND (obi.ORDER_NO like concat('%',#{keyword},'%') or PRODUCT_NAME like concat('%',#{keyword},'%') or CATEGORY_PATH like concat('%',#{keyword},'%'))
          ]]>
            </if>

            <if test="orderType == 300000">
                <![CDATA[
        AND (
          obi.ORDER_NO like concat('%',#{keyword},'%')
          or
          (obi.ORDER_NO in (select DISTINCT ORDER_NO from ORDER_SAMPLE where (SAMPLE_NAME like concat('%',#{keyword},'%')
          or SAMPLE_NAME_CN like concat('%',#{keyword},'%')
          or SAMPLE_NAME_EN like concat('%',#{keyword},'%')
          or PRODUCT_INFO like concat('%',#{keyword},'%')
          or PRODUCT_INFO_EN like concat('%',#{keyword},'%'))))
          or
          obi.ORDER_NO in (select DISTINCT ORDER_NO from ORDER_DETAIL where ITEM_NAME like concat('%',#{keyword},'%'))
          or
          obi.ORDER_NO in (select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=301000 and ORDER_NO like concat('%',#{keyword},'%'))
        )
          ]]>
            </if>
            <if test="orderType == 100000">
                <![CDATA[
        AND (
          (obi.ORDER_NO like concat('%',#{keyword},'%')  and obi.RELATE_ORDER_NO  is null )
               or
                obi.ORDER_NO in (select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=101000   and ORDER_NO like
                concat('%',#{keyword},'%'))
          or
          (obi.ORDER_NO in (select DISTINCT ORDER_NO from  ORDER_PRODUCT  where   PRODUCT_NAME like
           concat('%',#{keyword},'%')  and state = 1) )
           or
           (obi.ORDER_NO IN (select DISTINCT  ORDER_NO from ORDER_SAMPLE_FROM
            where
            (SAMPLE_VALUE like concat('%',#{keyword},'%') AND  SAMPLE_KEY = 'sampleName' )
            or
            (SAMPLE_VALUE like concat('%',#{keyword},'%') AND  SAMPLE_KEY = 'sampleDescription' )
                      or
            (SAMPLE_VALUE like concat('%',#{keyword},'%') AND  SAMPLE_KEY = 'sampleDescriptionEn' )
             or
            (SAMPLE_VALUE like concat('%',#{keyword},'%') AND  SAMPLE_KEY = 'modelName' )
             or
            (SAMPLE_VALUE like concat('%',#{keyword},'%') AND  SAMPLE_KEY = 'styleNo'  )
            or
            (SAMPLE_VALUE like concat('%',#{keyword},'%') AND  SAMPLE_KEY = 'brandName'  )
            or
            (SAMPLE_VALUE like concat('%',#{keyword},'%') AND  SAMPLE_KEY = 'sampleNameEn' )
            ) )
           or
           (obi.ORDER_NO IN (select DISTINCT ORDER_NO from ORDER_REPORT where  REPORT_COMPANY_NAME_CN LIKE  concat('%',#{keyword},'%'))
            )
             )

          ]]>
            </if>


        </if>

        <include refid="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper.betweenKey"/>
    </sql>

    <sql id="betweenKey">
        <if test="betweenKey != null">
            <if test="betweenKey.createDateStart!=null and betweenKey.createDateEnd!=null">
                AND obi.CREATE_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
            </if>
            <if test="betweenKey.finishDateStart!=null and betweenKey.finishDateEnd!=null">
               AND obi.STATE=80 AND obi.STATE_DATE between #{betweenKey.finishDateStart} and #{betweenKey.finishDateEnd}
            </if>
            <if test="betweenKey.confirmOrderDateStart!=null and betweenKey.confirmOrderDateEnd!=null">
                AND obi.CONFIRM_ORDER_DATE between #{betweenKey.confirmOrderDateStart} and #{betweenKey.confirmOrderDateEnd}
            </if>
        </if>
    </sql>


    <sql id="orderBy">
        <if test="orderType == 300000  " >
            order by IFNULL(CONFIRM_ORDER_DATE,CREATE_DATE)  desc,ORDER_ID desc
        </if>
        <if test="orderType != 300000 ">
            order by obi.CREATE_DATE desc,obi.ORDER_ID DESC
        </if>

    </sql>

    <sql id="useLimit">
        <if test="limitStart != null and limitEnd != null">
            limit #{limitStart},#{limitEnd}
        </if>
    </sql>


    <resultMap id="baseMap" type="com.sgs.ecom.member.dto.OrderBaseInfoCheckDTO">
        <id column="ORDER_ID" property="orderId" jdbcType="BIGINT"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR"/>
        <result column="BU" property="bu" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR"/>
        <result column="LAB_ID" property="labId" jdbcType="BIGINT"/>
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR"/>
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL"/>
        <result column="SALES_CODE" property="salesCode" jdbcType="VARCHAR"/>
        <result column="SALES_PHONE" property="salesPhone" jdbcType="VARCHAR"/>
        <result column="PROMO_INFO" property="promoInfo" jdbcType="VARCHAR"/>
        <result column="BOSS_NO" property="bossNo" jdbcType="VARCHAR"/>
        <result column="CUST_ID" property="custId" jdbcType="BIGINT"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR"/>
        <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR"/>
        <result column="COMPANY_ADDRESS_EN" property="companyAddressEn" jdbcType="VARCHAR"/>
        <result column="TOWN" property="town" jdbcType="VARCHAR"/>
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER"/>
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="baseSelectSql">
        ORDER_ID
        ,ORDER_NO,STATE,USER_ID,GROUP_NO,BU,ORDER_TYPE,LAB_ID,CS_CODE,REAL_AMOUNT,
       PROMO_INFO,SALES_CODE,SALES_PHONE,BOSS_NO,CUST_ID,COMPANY_NAME_EN,
COMPANY_ADDRESS_CN,COMPANY_ADDRESS_EN,TOWN,COMPANY_NAME,PROVINCE,CITY,USER_PHONE,USER_NAME,USER_EMAIL,IS_ELECTRON
    </sql>


    <select id="checkOrderBase" resultMap="baseMap">
        select
        <include refid="baseSelectSql"/>,
         SUB_STATE
        from ORDER_BASE_INFO
        where ORDER_NO = #{orderNo}
    </select>


    <select id="selectIndexDTO" resultMap="resultDTO">
        select
        <include refid="sqlListDTO"/>
        from ORDER_BASE_INFO
        where 1=1
        <if test="userId != null">
            AND USER_ID=#{userId}
        </if>
        order by IS_READ asc ,STATE_DATE desc limit 1
    </select>

	<select id="getUserOrderPayNum" parameterType="java.lang.Long" resultType="java.lang.Long">
        select count(ORDER_ID) ORDER_ID
        from ORDER_BASE_INFO
        where USER_ID=#{userId,jdbcType=BIGINT}
        and PAY_STATE in (1,2)
        and ORDER_TYPE = 100000
    </select>




    <resultMap id="resultWineDTO" type="com.sgs.ecom.member.dto.wine.WineShopDTO">
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"/>
        <result column="HOTEL_OFFICIAL_NAME" property="hotelOfficialName" jdbcType="VARCHAR"/>
        <result column="HOTEL_ADDRESS" property="hotelAddress" jdbcType="VARCHAR"/>
        <result column="COMPANY_LEGAL_NAME" property="companyLegalName" jdbcType="VARCHAR"/>
        <result column="COMPANY_LEGAL_ADDRESS" property="companyLegalAddress" jdbcType="VARCHAR"/>
        <result column="COMPANY_REG_NO" property="companyRegNo" jdbcType="VARCHAR"/>
        <result column="COUNTRY" property="country" jdbcType="VARCHAR"/>
        <result column="BRAND" property="brand" jdbcType="VARCHAR"/>
        <result column="NUMBER_OF_ROOMS" property="numberOfRooms" jdbcType="VARCHAR"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="HOTEL_CODE" property="hotelCode" jdbcType="VARCHAR"/>
        <result column="MANAGEMENT_TYPE" property="managementType" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CORE_NAME" property="coreName" jdbcType="VARCHAR"/>
        <result column="AREA_NAME" property="areaName" jdbcType="VARCHAR"/>
        <result column="CITY_PROPER" property="cityProper" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="WHID" property="whid" jdbcType="VARCHAR"/>
        <result column="BUSINESS_STATUS" property="businessStatus" jdbcType="VARCHAR"/>
        <result column="HOTEL_AREA" property="hotelArea" jdbcType="VARCHAR"/>
        <result column="HOTEL_PROVINCE" property="hotelProvince" jdbcType="VARCHAR"/>
        <result column="HOTEL_SUB_AREA" property="hotelSubArea" jdbcType="VARCHAR"/>
        <result column="START_DATE" property="startDate" jdbcType="TIMESTAMP"/>
        <result column="END_DATE" property="endDate" jdbcType="TIMESTAMP"/>
        <result column="PRICE" property="price" jdbcType="DECIMAL"/>
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
    </resultMap>
    <select id="selectWineListByMap" resultMap="resultWineDTO">
        SELECT
        distinct
        ( CASE WHEN obi.PAY_DATE IS NULL  THEN NULL ELSE obi.PAY_DATE END ) as START_DATE,
        ( CASE WHEN (SELECT OPERATOR_DATE FROM ORDER_OPERATOR_LOG ool WHERE ORDER_TYPE = 100000 AND OPERATOR_TYPE = 80 AND obi.ORDER_NO = ool.ORDER_NO) IS NULL  THEN null ELSE (SELECT OPERATOR_DATE FROM ORDER_OPERATOR_LOG ool WHERE ORDER_TYPE = 100000 AND OPERATOR_TYPE = 80 AND obi.ORDER_NO = ool.ORDER_NO) END ) as END_DATE,
        obi.ORDER_NO,
        obi.STATE,
        obi.CREATE_DATE,
        op.STORE_NAME,
        obi.REAL_AMOUNT AS PRICE,
        (CASE WHEN result.HOTEL_OFFICIAL_NAME IS NOT NULL   THEN result.HOTEL_OFFICIAL_NAME ELSE (CASE WHEN obi.STATE = 10 OR obi.STATE = 11 or obi.STATE = 91  THEN obi.COMPANY_NAME ELSE result.HOTEL_OFFICIAL_NAME END) END)  AS HOTEL_OFFICIAL_NAME,
        (CASE WHEN result.HOTEL_ADDRESS IS NOT NULL  THEN result.HOTEL_ADDRESS ELSE (CASE WHEN obi.STATE = 10 OR obi.STATE = 11 or obi.STATE = 91  THEN CONCAT(obi.PROVINCE,obi.CITY,obi.TOWN,obi.COMPANY_ADDRESS_CN) ELSE result.HOTEL_ADDRESS END) END)  AS HOTEL_ADDRESS,
        result.COMPANY_LEGAL_NAME,
        result.COMPANY_LEGAL_ADDRESS,
        result.COMPANY_REG_NO,
        result.COUNTRY,
        result.BRAND,
        result.NUMBER_OF_ROOMS,
        (CASE WHEN result.USER_NAME IS NOT NULL  THEN result.USER_NAME ELSE (CASE WHEN obi.STATE = 10 OR obi.STATE = 11 or obi.STATE = 91  THEN obi.USER_NAME ELSE result.USER_NAME END) END)  AS USER_NAME,
        (CASE WHEN result.USER_PHONE IS NOT NULL  THEN result.USER_PHONE ELSE (CASE WHEN obi.STATE = 10 OR obi.STATE = 11 or obi.STATE = 91  THEN obi.USER_PHONE ELSE result.USER_PHONE END) END)  AS USER_PHONE,
        result.USER_EMAIL,
        result.HOTEL_CODE,
        result.MANAGEMENT_TYPE,
        result.PROVINCE,
        result.CORE_NAME,
        result.AREA_NAME,
        result.CITY_PROPER,
        result.WHID,
        result.BUSINESS_STATUS,
        result.HOTEL_AREA,
        result.HOTEL_PROVINCE,
        result.HOTEL_SUB_AREA,
        result.CITY
        FROM
        ORDER_BASE_INFO obi
        LEFT JOIN ORDER_PRODUCT op ON obi.ORDER_NO = op.ORDER_NO and op.state = 1
        LEFT JOIN (
                SELECT
                       distinct
                    oaf.ORDER_NO,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'hotelOfficialName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) HOTEL_OFFICIAL_NAME,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'hotelAddress' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) HOTEL_ADDRESS,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'companyLegalName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) COMPANY_LEGAL_NAME,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'companyLegalAddress' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) COMPANY_LEGAL_ADDRESS,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'companyRegNo' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) COMPANY_REG_NO,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'country' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) COUNTRY,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'brand' THEN (CASE WHEN oaf.SAMPLE_EXPLAIN IS NULL THEN oaf.SAMPLE_VALUE ELSE oaf.SAMPLE_EXPLAIN END) ELSE '' END ) ) BRAND,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'brand' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) BRAND_CODE,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'numberOfRooms' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) NUMBER_OF_ROOMS,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'contactName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) USER_NAME,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'telephone' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) USER_PHONE,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'email' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) USER_EMAIL,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'hotelCode' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) HOTEL_CODE,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'managementType' THEN  (CASE WHEN oaf.SAMPLE_EXPLAIN IS NULL THEN oaf.SAMPLE_VALUE ELSE oaf.SAMPLE_EXPLAIN END)  ELSE '' END ) ) MANAGEMENT_TYPE,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'managementType' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) MANAGEMENT_TYPE_CODE,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'province' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) PROVINCE,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'coreName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) CORE_NAME,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'areaName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) AREA_NAME,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'cityProper' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) CITY_PROPER,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'whid' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) WHID,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'businessStatus' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) BUSINESS_STATUS,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'hotelArea' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) HOTEL_AREA,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'hotelProvince' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) HOTEL_PROVINCE,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'hotelSubArea' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) HOTEL_SUB_AREA,
                    max( ( CASE oaf.SAMPLE_KEY WHEN 'city' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) CITY
                FROM

                     ORDER_PRODUCT op,
                  order_base_info obii,
                    <if test="finishDate != null  and finishDate ==1 ">
                        ORDER_OPERATOR_LOG ool,
                    </if>
                        ORDER_SAMPLE_FROM oaf
                WHERE
                    obii.ORDER_NO = op.ORDER_NO
        AND obii.ORDER_NO = oaf.ORDER_NO
        AND   obii.ORDER_TYPE = 100000
                    AND obii.IS_TEST = 0
        and op.state = 1
        <if test="finishDate != null and finishDate ==1 ">
                        AND ool.ORDER_NO = oaf.ORDER_NO
                        <if test="startDate != null and startDate !='' ">
                            AND DATE_FORMAT(ool.OPERATOR_DATE,'%Y-%m-%d %H:%i:%s') &gt;=  #{startDate}
                        </if>
                        <if test="endDate != null and endDate !='' ">
                            AND DATE_FORMAT(ool.OPERATOR_DATE,'%Y-%m-%d %H:%i:%s') &lt;=  #{endDate}
                        </if>
                        AND  ool.OPERATOR_TYPE = 80
                    </if>
                    <if test="ProdIdList != null ">
                        AND  op.PROD_ID in
                        <foreach collection="ProdIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                  AND oaf.STATE = 1
                GROUP BY oaf.ORDER_NO
            ) result ON obi.ORDER_NO = result.ORDER_NO
                    <if test="finishDate != null and finishDate ==1 ">
                        LEFT JOIN ORDER_OPERATOR_LOG ool ON obi.ORDER_NO = ool.ORDER_NO
                    </if>

        <include refid="wineQueryWhere"/>
        ORDER BY obi.CREATE_DATE DESC
    </select>

    <resultMap id="indexMap" type="com.sgs.ecom.member.dto.custom.StateAndNumDTO" >
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="NUM" property="num" jdbcType="BIGINT" />
    </resultMap>

    <select id="selectWineStateByMap"  resultMap="indexMap"  >
        SELECT
               distinct
        if(obi.state=12,13,obi.state) STATE,count(distinct obi.ORDER_NO) NUM
        FROM
        ORDER_BASE_INFO obi
        LEFT JOIN ORDER_PRODUCT op ON obi.ORDER_NO = op.ORDER_NO and op.state = 1
        LEFT JOIN (
        SELECT
          distinct
        oaf.ORDER_NO,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'hotelOfficialName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) HOTEL_OFFICIAL_NAME,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'hotelAddress' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) HOTEL_ADDRESS,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'companyLegalName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) COMPANY_LEGAL_NAME,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'companyLegalAddress' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) COMPANY_LEGAL_ADDRESS,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'companyRegNo' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) COMPANY_REG_NO,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'country' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) COUNTRY,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'brand' THEN (CASE WHEN oaf.SAMPLE_EXPLAIN IS NULL THEN oaf.SAMPLE_VALUE ELSE oaf.SAMPLE_EXPLAIN END) ELSE '' END ) ) BRAND,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'brand' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) BRAND_CODE,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'numberOfRooms' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) NUMBER_OF_ROOMS,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'contactName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) USER_NAME,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'telephone' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) USER_PHONE,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'email' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) USER_EMAIL,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'hotelCode' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) HOTEL_CODE,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'managementType' THEN  (CASE WHEN oaf.SAMPLE_EXPLAIN IS NULL THEN oaf.SAMPLE_VALUE ELSE oaf.SAMPLE_EXPLAIN END)  ELSE '' END ) ) MANAGEMENT_TYPE,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'managementType' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) MANAGEMENT_TYPE_CODE,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'province' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) PROVINCE,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'coreName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) CORE_NAME,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'areaName' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) AREA_NAME,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'cityProper' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) CITY_PROPER,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'whid' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) WHID,
        max( ( CASE oaf.SAMPLE_KEY WHEN 'city' THEN oaf.SAMPLE_VALUE ELSE '' END ) ) CITY
        FROM
                     ORDER_PRODUCT op,
                  order_base_info obii,
                    <if test="finishDate != null  and finishDate ==1 ">
                        ORDER_OPERATOR_LOG ool,
                    </if>
                        ORDER_SAMPLE_FROM oaf
                WHERE
                    obii.ORDER_NO = op.ORDER_NO
        AND obii.ORDER_NO = oaf.ORDER_NO
        AND   obii.ORDER_TYPE = 100000
        AND obii.IS_TEST = 0
        and op.state = 1
        <if test="finishDate != null and finishDate ==1 ">
            AND ool.ORDER_NO = oaf.ORDER_NO
            <if test="startDate != null and startDate !='' ">
                AND DATE_FORMAT(ool.OPERATOR_DATE,'%Y-%m-%d %H:%i:%s') &gt;=  #{startDate}
            </if>
            <if test="endDate != null and endDate !='' ">
                AND DATE_FORMAT(ool.OPERATOR_DATE,'%Y-%m-%d %H:%i:%s') &lt;=  #{endDate}
            </if>
            AND  ool.OPERATOR_TYPE = 80
        </if>
        <if test="ProdIdList != null ">
            AND  op.PROD_ID in
            <foreach collection="ProdIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="stateList != null ">
            AND obi.STATE in
            <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND oaf.STATE = 1
        GROUP BY oaf.ORDER_NO
        ) result ON obi.ORDER_NO = result.ORDER_NO
        <if test="finishDate != null and finishDate ==1 ">
            LEFT JOIN ORDER_OPERATOR_LOG ool ON obi.ORDER_NO = ool.ORDER_NO
        </if>
        <include refid="wineQueryWhere"/>
        group by if(obi.state=12,13,obi.state)
    </select>

    <sql id="wineQueryWhere">
        WHERE
        obi.ORDER_TYPE = 100000
        AND obi.IS_TEST = 0
        <if test="hotelOfficialName != null and hotelOfficialName !='' ">
            AND (result.HOTEL_OFFICIAL_NAME LIKE concat('%',#{hotelOfficialName},'%') OR result.HOTEL_CODE LIKE concat('%',#{hotelOfficialName},'%'))
        </if>
        <if test="(provinceList != null  and   provinceList.size()>0) or  (cityList != null   and   cityList.size()>0) ">
            AND (

            <if test="provinceList != null  and   provinceList.size()>0 ">
                <foreach collection="provinceList" index="index" item="item" separator="OR">
                    result.PROVINCE =#{item}
                </foreach>
            </if>
            <if test="cityList != null   and   cityList.size()>0">
                <if test="provinceList != null  and   provinceList.size()>0 ">
                    OR
                </if>
                <foreach collection="cityList" index="index" item="item" separator="OR">
                    result.CITY =#{item}
                </foreach>
            </if>
            )
        </if>
        <if test="brandList != null  and   brandList.size()>0">
            AND
            <foreach collection="brandList" index="index" item="item" open="(" separator="OR" close=")">
                result.BRAND_CODE =#{item}
            </foreach>
        </if>
        <if test="managementTypeList != null  and   managementTypeList.size()>0">
            AND
            <foreach collection="managementTypeList" index="index" item="item" open="(" separator="OR" close=")">
                result.MANAGEMENT_TYPE_CODE =#{item}
            </foreach>
        </if>
        <if test="finishDate != null and finishDate ==1 ">
            AND  obi.ORDER_NO = ool.ORDER_NO
            <if test="startDate != null and startDate !='' ">
                AND DATE_FORMAT(ool.OPERATOR_DATE,'%Y-%m-%d %H:%i:%s') &gt;=  #{startDate}

            </if>
            <if test="endDate != null and endDate !='' ">
                AND DATE_FORMAT(ool.OPERATOR_DATE,'%Y-%m-%d %H:%i:%s') &lt;=  #{endDate}
            </if>
            AND  ool.OPERATOR_TYPE = 80
        </if>
        <if test="stateList != null  and   stateList.size>0 ">
            AND obi.STATE in
            <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ProdIdList != null ">
            AND  op.PROD_ID in
            <foreach collection="ProdIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>



    </sql>


    <insert id="insertReturnKey" keyProperty="orderId" parameterType="com.sgs.ecom.member.entity.order.OrderBaseInfo" useGeneratedKeys="true">
        insert into ORDER_BASE_INFO (
        ORDER_NO, ORDER_TYPE, ORDER_AMOUNT, SHOP_DIS_AMOUNT, QUESTION_ID, LAB_ID,
        LAB_NAME, DISCOUNT_AMOUNT, REAL_AMOUNT, STATE, PRO_STATE, PAY_STATE, USER_ID,
        USER_PHONE, USER_NAME, USER_EMAIL, COMPANY_NAME, PROVINCE, CITY, RELATE_ORDER_NO,
        PAY_DATE, CREATE_DATE, STATE_DATE, PLATFORM, PLATFORM_ORDER, CS_CODE, CS_BRANCH, SERVICE_AMOUNT,
        OFFER_DATE, ORDER_EXP_DATE, REPORT_LUA, REPORT_FORM, TEST_CYCLE, IS_URGENT, CATEGORY_PATH,
        BU, USER_SEX, CS_EMAIL, BUSINESS_PERSON_EMAIL, IS_READ, RECOMMEND_REASON, SAMPLE_REQUIREMENTS,
        GROUP_NO, HIS_STATE, CS_NAME, LINE_ID, BUSINESS_LINE, URGENT_AMOUNT, TMP_GROUP_NO, IS_REMIND,
        CS_NAME_EN, PRODUCT_NAME, CATAGORY_ID, SUB_STATE, TOTAL_NUMS, PRODUCT_IMG, IS_TEST, IS_INVOICE,
        AUDIT_CODE, REPORT_LUA_CODE, REPORT_FORM_CODE, IS_DELETE, IS_PAY_RECEIVED, TEST_LABEL,
        LAST_RESPONSE_DATE, ORDER_SOURCE, ORDER_SOURCE_FROM, CONFIRM_ORDER_DATE, PROMO_INFO,
        SALES_CODE, SALES_PHONE, FROM_SOURCE, FROM_URL, BOSS_NO, CUST_ID, MONTH_PAY, CURRENCY,
        COMPANY_NAME_EN, COMPANY_ADDRESS_CN, COMPANY_ADDRESS_EN, TOWN, IS_ELECTRON, OPERATOR_CODE
        )
        values (
        #{orderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, #{orderAmount,jdbcType=DECIMAL},
        #{shopDisAmount,jdbcType=DECIMAL}, #{questionId,jdbcType=VARCHAR}, #{labId,jdbcType=VARCHAR},
        #{labName,jdbcType=VARCHAR}, #{discountAmount,jdbcType=DECIMAL}, #{realAmount,jdbcType=DECIMAL},
        #{state,jdbcType=INTEGER}, #{proState,jdbcType=INTEGER}, #{payState,jdbcType=INTEGER},
        #{userId,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR},
        #{userEmail,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR},
        #{city,jdbcType=VARCHAR}, #{relateOrderNo,jdbcType=VARCHAR},
        #{payDate,jdbcType=TIMESTAMP}, #{createDate,jdbcType=TIMESTAMP}, #{stateDate,jdbcType=TIMESTAMP},
        #{platform,jdbcType=VARCHAR}, #{platformOrder,jdbcType=VARCHAR}, #{csCode,jdbcType=VARCHAR},
        #{csBranch,jdbcType=VARCHAR}, #{serviceAmount,jdbcType=DECIMAL}, #{offerDate,jdbcType=TIMESTAMP},
        #{orderExpDate,jdbcType=TIMESTAMP}, #{reportLua,jdbcType=VARCHAR}, #{reportForm,jdbcType=VARCHAR},
        #{testCycle,jdbcType=VARCHAR}, #{isUrgent,jdbcType=INTEGER}, #{categoryPath,jdbcType=VARCHAR},
        #{bu,jdbcType=VARCHAR}, #{userSex,jdbcType=INTEGER}, #{csEmail,jdbcType=VARCHAR},
        #{businessPersonEmail,jdbcType=VARCHAR}, #{isRead,jdbcType=INTEGER}, #{recommendReason,jdbcType=VARCHAR},
        #{sampleRequirements,jdbcType=VARCHAR}, #{groupNo,jdbcType=VARCHAR}, #{hisState,jdbcType=INTEGER},
        #{csName,jdbcType=VARCHAR}, #{lineId,jdbcType=VARCHAR}, #{businessLine,jdbcType=VARCHAR},
        #{urgentAmount,jdbcType=DECIMAL},
        #{tmpGroupNo,jdbcType=VARCHAR}, #{isRemind,jdbcType=INTEGER}, #{csNameEn,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR}, #{catagoryId,jdbcType=VARCHAR}, #{subState,jdbcType=INTEGER},
        #{totalNums,jdbcType=INTEGER}, #{productImg,jdbcType=VARCHAR}, #{isTest,jdbcType=INTEGER},
        #{isInvoice,jdbcType=INTEGER}, #{auditCode,jdbcType=VARCHAR}, #{reportLuaCode,jdbcType=VARCHAR},
        #{reportFormCode,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, #{isPayReceived,jdbcType=INTEGER},
        #{testLabel,jdbcType=VARCHAR}, #{lastResponseDate,jdbcType=TIMESTAMP}, #{orderSource,jdbcType=VARCHAR},
        #{orderSourceFrom,jdbcType=VARCHAR}, #{confirmOrderDate,jdbcType=TIMESTAMP}, #{promoInfo,jdbcType=VARCHAR},
        #{salesCode,jdbcType=VARCHAR}, #{salesPhone,jdbcType=VARCHAR},
        #{fromSource,jdbcType=VARCHAR}, #{fromUrl,jdbcType=VARCHAR}, #{bossNo,jdbcType=VARCHAR},
        #{custId,jdbcType=VARCHAR}, #{monthPay,jdbcType=DECIMAL}, #{currency,jdbcType=VARCHAR},
        #{companyNameEn,jdbcType=VARCHAR}, #{companyAddressCn,jdbcType=VARCHAR}, #{companyAddressEn,jdbcType=VARCHAR},
        #{town,jdbcType=VARCHAR}, #{isElectron,jdbcType=INTEGER}, #{operatorCode,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertEntity" parameterType="com.sgs.ecom.member.entity.order.OrderBaseInfo">
        <selectKey resultType="java.lang.Long" keyProperty="orderId" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into ORDER_BASE_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                ORDER_ID,
            </if>
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="orderType != null">
                ORDER_TYPE,
            </if>
            <if test="orderAmount != null">
                ORDER_AMOUNT,
            </if>
            <if test="shopDisAmount != null">
                SHOP_DIS_AMOUNT,
            </if>
            <if test="questionId != null">
                QUESTION_ID,
            </if>
            <if test="labId != null">
                LAB_ID,
            </if>
            <if test="labName != null">
                LAB_NAME,
            </if>
            <if test="discountAmount != null">
                DISCOUNT_AMOUNT,
            </if>
            <if test="realAmount != null">
                REAL_AMOUNT,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="proState != null">
                PRO_STATE,
            </if>
            <if test="payState != null">
                PAY_STATE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="userPhone != null">
                USER_PHONE,
            </if>
            <if test="userName != null">
                USER_NAME,
            </if>
            <if test="userEmail != null">
                USER_EMAIL,
            </if>
            <if test="companyName != null">
                COMPANY_NAME,
            </if>
            <if test="province != null">
                PROVINCE,
            </if>
            <if test="city != null">
                CITY,
            </if>
            <if test="relateOrderNo != null">
                RELATE_ORDER_NO,
            </if>
            <if test="payDate != null">
                PAY_DATE,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="stateDate != null">
                STATE_DATE,
            </if>
            <if test="platform != null">
                PLATFORM,
            </if>
            <if test="platformOrder != null">
                PLATFORM_ORDER,
            </if>
            <if test="csCode != null">
                CS_CODE,
            </if>
            <if test="csPhone != null">
                CS_PHONE,
            </if>
            <if test="csBranch != null">
                CS_BRANCH,
            </if>
            <if test="serviceAmount != null">
                SERVICE_AMOUNT,
            </if>
            <if test="offerDate != null">
                OFFER_DATE,
            </if>
            <if test="orderExpDate != null">
                ORDER_EXP_DATE,
            </if>
            <if test="reportLua != null">
                REPORT_LUA,
            </if>
            <if test="reportForm != null">
                REPORT_FORM,
            </if>
            <if test="testCycle != null">
                TEST_CYCLE,
            </if>
            <if test="isUrgent != null">
                IS_URGENT,
            </if>
            <if test="categoryPath != null">
                CATEGORY_PATH,
            </if>
            <if test="bu != null">
                BU,
            </if>
            <if test="userSex != null">
                USER_SEX,
            </if>
            <if test="csEmail != null">
                CS_EMAIL,
            </if>
            <if test="businessPersonEmail != null">
                BUSINESS_PERSON_EMAIL,
            </if>
            <if test="isRead != null">
                IS_READ,
            </if>
            <if test="recommendReason != null">
                RECOMMEND_REASON,
            </if>
            <if test="sampleRequirements != null">
                SAMPLE_REQUIREMENTS,
            </if>
            <if test="groupNo != null">
                GROUP_NO,
            </if>
            <if test="hisState != null">
                HIS_STATE,
            </if>
            <if test="csName != null">
                CS_NAME,
            </if>
            <if test="lineId != null">
                LINE_ID,
            </if>
            <if test="applicationLineId != null">
                APPLICATION_LINE_ID,
            </if>
            <if test="businessLine != null">
                BUSINESS_LINE,
            </if>
            <if test="urgentAmount != null">
                URGENT_AMOUNT,
            </if>
            <if test="tmpGroupNo != null">
                TMP_GROUP_NO,
            </if>
            <if test="isRemind != null">
                IS_REMIND,
            </if>
            <if test="csNameEn != null">
                CS_NAME_EN,
            </if>
            <if test="productName != null">
                PRODUCT_NAME,
            </if>
            <if test="catagoryId != null">
                CATAGORY_ID,
            </if>
            <if test="subState != null">
                SUB_STATE,
            </if>
            <if test="totalNums != null">
                TOTAL_NUMS,
            </if>
            <if test="productImg != null">
                PRODUCT_IMG,
            </if>
            <if test="isTest != null">
                IS_TEST,
            </if>
            <if test="isInvoice != null">
                IS_INVOICE,
            </if>
            <if test="auditCode != null">
                AUDIT_CODE,
            </if>
            <if test="reportLuaCode != null">
                REPORT_LUA_CODE,
            </if>
            <if test="reportFormCode != null">
                REPORT_FORM_CODE,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="isPayReceived != null">
                IS_PAY_RECEIVED,
            </if>
            <if test="testLabel != null">
                TEST_LABEL,
            </if>
            <if test="lastResponseDate != null">
                LAST_RESPONSE_DATE,
            </if>
            <if test="orderSource != null">
                ORDER_SOURCE,
            </if>
            <if test="orderSourceFrom != null">
                ORDER_SOURCE_FROM,
            </if>
            <if test="confirmOrderDate != null">
                CONFIRM_ORDER_DATE,
            </if>
            <if test="promoInfo != null">
                PROMO_INFO,
            </if>
            <if test="salesCode != null">
                SALES_CODE,
            </if>
            <if test="salesPhone != null">
                SALES_PHONE,
            </if>
            <if test="fromSource != null">
                FROM_SOURCE,
            </if>
            <if test="fromUrl != null">
                FROM_URL,
            </if>
            <if test="bossNo != null">
                BOSS_NO,
            </if>
            <if test="custId != null">
                CUST_ID,
            </if>
            <if test="monthPay != null">
                MONTH_PAY,
            </if>
            <if test="currency != null">
                CURRENCY,
            </if>
            <if test="companyNameEn != null">
                COMPANY_NAME_EN,
            </if>
            <if test="companyAddressCn != null">
                COMPANY_ADDRESS_CN,
            </if>
            <if test="companyAddressEn != null">
                COMPANY_ADDRESS_EN,
            </if>
            <if test="town != null">
                TOWN,
            </if>
            <if test="isElectron != null">
                IS_ELECTRON,
            </if>
            <if test="operatorCode != null">
                OPERATOR_CODE,
            </if>
            <if test="salesEmail != null">
                SALES_EMAIL,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="shopDisAmount != null">
                #{shopDisAmount,jdbcType=DECIMAL},
            </if>
            <if test="questionId != null">
                #{questionId,jdbcType=BIGINT},
            </if>
            <if test="labId != null">
                #{labId,jdbcType=BIGINT},
            </if>
            <if test="labName != null">
                #{labName,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                #{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="proState != null">
                #{proState,jdbcType=INTEGER},
            </if>
            <if test="payState != null">
                #{payState,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userPhone != null">
                #{userPhone,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userEmail != null">
                #{userEmail,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="relateOrderNo != null">
                #{relateOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payDate != null">
                #{payDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="platformOrder != null">
                #{platformOrder,jdbcType=VARCHAR},
            </if>
            <if test="csCode != null">
                #{csCode,jdbcType=VARCHAR},
            </if>
            <if test="csPhone != null">
                #{csPhone,jdbcType=VARCHAR},
            </if>
            <if test="csBranch != null">
                #{csBranch,jdbcType=VARCHAR},
            </if>
            <if test="serviceAmount != null">
                #{serviceAmount,jdbcType=DECIMAL},
            </if>
            <if test="offerDate != null">
                #{offerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderExpDate != null">
                #{orderExpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportLua != null">
                #{reportLua,jdbcType=VARCHAR},
            </if>
            <if test="reportForm != null">
                #{reportForm,jdbcType=VARCHAR},
            </if>
            <if test="testCycle != null">
                #{testCycle,jdbcType=DECIMAL},
            </if>
            <if test="isUrgent != null">
                #{isUrgent,jdbcType=TINYINT},
            </if>
            <if test="categoryPath != null">
                #{categoryPath,jdbcType=VARCHAR},
            </if>
            <if test="bu != null">
                #{bu,jdbcType=VARCHAR},
            </if>
            <if test="userSex != null">
                #{userSex,jdbcType=TINYINT},
            </if>
            <if test="csEmail != null">
                #{csEmail,jdbcType=VARCHAR},
            </if>
            <if test="businessPersonEmail != null">
                #{businessPersonEmail,jdbcType=VARCHAR},
            </if>
            <if test="isRead != null">
                #{isRead,jdbcType=TINYINT},
            </if>
            <if test="recommendReason != null">
                #{recommendReason,jdbcType=VARCHAR},
            </if>
            <if test="sampleRequirements != null">
                #{sampleRequirements,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                #{groupNo,jdbcType=VARCHAR},
            </if>
            <if test="hisState != null">
                #{hisState,jdbcType=INTEGER},
            </if>
            <if test="csName != null">
                #{csName,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                #{lineId,jdbcType=BIGINT},
            </if>
            <if test="applicationLineId != null">
                #{applicationLineId,jdbcType=BIGINT},
            </if>
            <if test="businessLine != null">
                #{businessLine,jdbcType=VARCHAR},
            </if>
            <if test="urgentAmount != null">
                #{urgentAmount,jdbcType=DECIMAL},
            </if>
            <if test="tmpGroupNo != null">
                #{tmpGroupNo,jdbcType=VARCHAR},
            </if>
            <if test="isRemind != null">
                #{isRemind,jdbcType=TINYINT},
            </if>
            <if test="csNameEn != null">
                #{csNameEn,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="catagoryId != null">
                #{catagoryId,jdbcType=BIGINT},
            </if>
            <if test="subState != null">
                #{subState,jdbcType=INTEGER},
            </if>
            <if test="totalNums != null">
                #{totalNums,jdbcType=INTEGER},
            </if>
            <if test="productImg != null">
                #{productImg,jdbcType=VARCHAR},
            </if>
            <if test="isTest != null">
                #{isTest,jdbcType=TINYINT},
            </if>
            <if test="isInvoice != null">
                #{isInvoice,jdbcType=TINYINT},
            </if>
            <if test="auditCode != null">
                #{auditCode,jdbcType=VARCHAR},
            </if>
            <if test="reportLuaCode != null">
                #{reportLuaCode,jdbcType=VARCHAR},
            </if>
            <if test="reportFormCode != null">
                #{reportFormCode,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="isPayReceived != null">
                #{isPayReceived,jdbcType=TINYINT},
            </if>
            <if test="testLabel != null">
                #{testLabel},
            </if>
            <if test="lastResponseDate != null">
                #{lastResponseDate},
            </if>
            <if test="orderSource != null">
                #{orderSource},
            </if>
            <if test="orderSourceFrom != null">
                #{orderSourceFrom},
            </if>
            <if test="confirmOrderDate != null">
                #{confirmOrderDate},
            </if>
            <if test="promoInfo != null">
                #{promoInfo},
            </if>
            <if test="salesCode != null">
                #{salesCode},
            </if>
            <if test="salesPhone != null">
                #{salesPhone},
            </if>
            <if test="fromSource != null">
                #{fromSource,jdbcType=VARCHAR},
            </if>
            <if test="fromUrl != null">
                #{fromUrl,jdbcType=VARCHAR},
            </if>
            <if test="bossNo != null">
                #{bossNo},
            </if>
            <if test="custId != null">
                #{custId},
            </if>
            <if test="monthPay != null">
                #{monthPay},
            </if>
            <if test="currency != null">
                #{currency},
            </if>
            <if test="companyNameEn != null">
                #{companyNameEn,jdbcType=VARCHAR},
            </if>
            <if test="companyAddressCn != null">
                #{companyAddressCn,jdbcType=VARCHAR},
            </if>
            <if test="companyAddressEn != null">
                #{companyAddressEn,jdbcType=VARCHAR},
            </if>
            <if test="town != null">
                #{town,jdbcType=VARCHAR},
            </if>
            <if test="isElectron != null">
                #{isElectron},
            </if>
            <if test="operatorCode != null">
                #{operatorCode},
            </if>
            <if test="salesEmail != null">
                #{salesEmail},
            </if>
        </trim>
    </insert>

    <update id="updateEntity" parameterType="com.sgs.ecom.member.entity.order.OrderBaseInfo">
        update ORDER_BASE_INFO
        <set>
            <if test="orderNo != null">
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                ORDER_TYPE = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="questionId != null">
                QUESTION_ID = #{questionId,jdbcType=BIGINT},
            </if>
            <if test="labId != null">
                LAB_ID = #{labId,jdbcType=BIGINT},
            </if>
            <if test="labName != null">
                LAB_NAME = #{labName,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                DISCOUNT_AMOUNT = #{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=INTEGER},
            </if>
            <if test="proState != null">
                PRO_STATE = #{proState,jdbcType=INTEGER},
            </if>
            <if test="payState != null">
                PAY_STATE = #{payState,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userPhone != null">
                USER_PHONE = #{userPhone,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                USER_NAME = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userEmail != null">
                USER_EMAIL = #{userEmail,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                PROVINCE = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                CITY = #{city,jdbcType=VARCHAR},
            </if>
            <if test="relateOrderNo != null">
                RELATE_ORDER_NO = #{relateOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payDate != null">
                PAY_DATE = #{payDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="platform != null">
                PLATFORM = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="platformOrder != null">
                PLATFORM_ORDER = #{platformOrder,jdbcType=VARCHAR},
            </if>
            <if test="csCode != null">
                CS_CODE = #{csCode,jdbcType=VARCHAR},
            </if>
            <if test="csBranch != null">
                CS_BRANCH = #{csBranch,jdbcType=VARCHAR},
            </if>
            <if test="serviceAmount != null">
                SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
            </if>
            <if test="offerDate != null">
                OFFER_DATE = #{offerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderExpDate != null">
                ORDER_EXP_DATE = #{orderExpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportLua != null">
                REPORT_LUA = #{reportLua,jdbcType=VARCHAR},
            </if>
            <if test="reportForm != null">
                REPORT_FORM = #{reportForm,jdbcType=VARCHAR},
            </if>
            <if test="testCycle != null">
                TEST_CYCLE = #{testCycle,jdbcType=DECIMAL},
            </if>
            <if test="isUrgent != null">
                IS_URGENT = #{isUrgent,jdbcType=TINYINT},
            </if>
            <if test="categoryPath != null">
                CATEGORY_PATH = #{categoryPath,jdbcType=VARCHAR},
            </if>
            <if test="bu != null">
                BU = #{bu,jdbcType=VARCHAR},
            </if>
            <if test="userSex != null">
                USER_SEX = #{userSex,jdbcType=TINYINT},
            </if>
            <if test="csEmail != null">
                CS_EMAIL = #{csEmail,jdbcType=VARCHAR},
            </if>
            <if test="businessPersonEmail != null">
                BUSINESS_PERSON_EMAIL = #{businessPersonEmail},
            </if>
            <if test="isRead != null">
                IS_READ = #{isRead,jdbcType=TINYINT},
            </if>
            <if test="recommendReason != null">
                RECOMMEND_REASON = #{recommendReason,jdbcType=VARCHAR},
            </if>
            <if test="sampleRequirements != null">
                SAMPLE_REQUIREMENTS = #{sampleRequirements,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                GROUP_NO = #{groupNo,jdbcType=VARCHAR},
            </if>
            <if test="hisState != null">
                HIS_STATE = #{hisState,jdbcType=INTEGER},
            </if>
            <if test="csName != null">
                CS_NAME = #{csName,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                LINE_ID = #{lineId,jdbcType=BIGINT},
            </if>
            <if test="applicationLineId != null">
                APPLICATION_LINE_ID = #{applicationLineId,jdbcType=BIGINT},
            </if>
            <if test="businessLine != null">
                BUSINESS_LINE = #{businessLine,jdbcType=VARCHAR},
            </if>
            <if test="urgentAmount != null">
                URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
            </if>
            <if test="tmpGroupNo != null">
                TMP_GROUP_NO = #{tmpGroupNo,jdbcType=VARCHAR},
            </if>
            <if test="isRemind != null">
                IS_REMIND = #{isRemind,jdbcType=TINYINT},
            </if>
            <if test="csNameEn != null">
                CS_NAME_EN = #{csNameEn,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="catagoryId != null">
                CATAGORY_ID = #{catagoryId,jdbcType=BIGINT},
            </if>
            <if test="subState != null">
                SUB_STATE = #{subState,jdbcType=INTEGER},
            </if>
            <if test="refundState != null">
                REFUND_STATE = #{refundState,jdbcType=INTEGER},
            </if>
            <if test="totalNums != null">
                TOTAL_NUMS = #{totalNums,jdbcType=INTEGER},
            </if>
            <if test="productImg != null">
                PRODUCT_IMG = #{productImg,jdbcType=VARCHAR},
            </if>
            <if test="abstractCustcode != null">
                ABSTRACT_CUSTCODE = #{abstractCustcode,jdbcType=VARCHAR},
            </if>
            <if test="isTest != null">
                IS_TEST = #{isTest,jdbcType=TINYINT},
            </if>
            <if test="isInvoice != null">
                IS_INVOICE = #{isInvoice,jdbcType=TINYINT},
            </if>
            <if test="auditCode != null">
                AUDIT_CODE = #{auditCode,jdbcType=VARCHAR},
            </if>
            <if test="reportLuaCode != null">
                REPORT_LUA_CODE = #{reportLuaCode,jdbcType=VARCHAR},
            </if>
            <if test="reportFormCode != null">
                REPORT_FORM_CODE = #{reportFormCode,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="isPayReceived != null">
                IS_PAY_RECEIVED = #{isPayReceived,jdbcType=TINYINT},
            </if>
            <if test="accountNo != null">
                ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="payMethod != null">
                PAY_METHOD = #{payMethod},
            </if>
            <if test="operatorSource != null">
                OPERATOR_SOURCE = #{operatorSource},
            </if>
            <if test="confirmOrderDate != null">
                CONFIRM_ORDER_DATE = #{confirmOrderDate},
            </if>
            <if test="promoInfo != null">
                PROMO_INFO= #{promoInfo},
            </if>
            <if test="salesCode != null">
                SALES_CODE=#{salesCode},
            </if>
            <if test="salesPhone != null">
                SALES_PHONE= #{salesPhone},
            </if>
            <if test="bossNo != null">
                BOSS_NO= #{bossNo},
            </if>
            <if test="custId != null">
                CUST_ID= #{custId},
            </if>
            <if test="monthPay != null">
                MONTH_PAY= #{monthPay},
            </if>
            <if test="currency != null">
                CURRENCY= #{currency},
            </if>
            <if test="userPhone != null">
                USER_PHONE= #{userPhone},
            </if>
            <if test="userName != null">
                USER_NAME= #{userName},
            </if>
            <if test="userEmail != null">
                USER_EMAIL= #{userEmail},
            </if>
            <if test="companyName != null">
                COMPANY_NAME= #{companyName},
            </if>
            <if test="province != null">
                PROVINCE= #{province},
            </if>
            <if test="city != null">
                CITY= #{city},
            </if>
            <if test="companyNameEn != null">
                COMPANY_NAME_EN= #{companyNameEn},
            </if>
            <if test="companyAddressCn != null">
                COMPANY_ADDRESS_CN= #{companyAddressCn},
            </if>
            <if test="companyAddressEn != null">
                COMPANY_ADDRESS_EN= #{companyAddressEn},
            </if>
            <if test="town != null">
                TOWN= #{town},
            </if>
            <if test="noticeNum!= null">
                NOTICE_NUM = #{noticeNum},
            </if>
            <if test="isElectron!= null">
                IS_ELECTRON = #{isElectron},
            </if>
            <if test="operatorCode!= null">
                OPERATOR_CODE = #{operatorCode},
            </if>
            <if test="platformAmount!= null">
                PLATFORM_AMOUNT = #{platformAmount},
            </if>
            <if test="salesEmail != null">
                SALES_EMAIL = #{salesEmail},
            </if>

        </set>
        where ORDER_ID = #{orderId,jdbcType=BIGINT}
        <if test="confirmUseState">
            AND STATE=#{confirmUseState}
        </if>
    </update>


    <select id="selectBaseByOrderNo"  resultMap="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoCustomMapper.baseOrder"  >
        select
        <include refid="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoCustomMapper.baseSql" />
        from ORDER_BASE_INFO
        where ORDER_NO=#{orderNo}
    </select>


    <resultMap id="lastOrder" type="com.sgs.ecom.member.dto.order.ToOrderDTO" >
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
    </resultMap>


    <select id="getLastOrderNoByInquiry"  resultMap="lastOrder" parameterType="java.util.List" >
        select ORDER_NO,RELATE_ORDER_NO from order_base_info where ORDER_ID in (
            select max(ORDER_ID) from ORDER_BASE_INFO obi where  obi.RELATE_ORDER_NO in
            <foreach collection="list"  item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
         group by obi.RELATE_ORDER_NO
        )
    </select>

    <select id="selectNewListByReq" resultMap="resultDTO">
        select
        <include refid="sqlListDTO"/>
        from ORDER_BASE_INFO obi
        <include refid="baseNewQueryWhere"/>
        <include refid="includeNewQueryWhere"/>
        order by obi.CREATE_DATE desc
        <include refid="useLimit"/>
    </select>
    <select id="selectListExpByMap" resultMap="RSTSEXPResultMap">

        SELECT
            allOrder.ORDER_ID,
            allOrder.ORDER_NO ,
            allOrder.RELATE_ORDER_NO ,
            allOrder.CREATE_DATE,
            allOrder.CONFIRM_ORDER_DATE,
            allOrder.STATE,
            allOrder.MONTH_PAY,
            allOrder.PAY_STATE,
            allOrder.company_NAME_CN AS COMPANY_NAME_CN,
            allOrder.CURRENCY,
            allOrder.REAL_AMOUNT,
            allOrder.LAB_NAME,
            allOrder.LINK_PERSON,
            allOrder.LINK_PHONE,
            allOrder.LINK_EMAIL

        from (
                 SELECT
                     '主订单',
                     obi.ORDER_ID,
                     obi.ORDER_NO as RELATE_ORDER_NO,
                     obi.RELATE_ORDER_NO as ORDER_NO,
                     obi.CREATE_DATE,
                     obi.CONFIRM_ORDER_DATE,
                     obi.STATE,
                     obi.MONTH_PAY,
                     obi.PAY_METHOD AS PAY_STATE,
                     oaf.company_NAME_CN AS COMPANY_NAME_CN,
                     obi.CURRENCY,
        IF(obi.PLATFORM_AMOUNT is  null ,obi.REAL_AMOUNT,obi.PLATFORM_AMOUNT)  AS 'REAL_AMOUNT',
                     obi.LAB_NAME,
                     oaf.LINK_PERSON,
                     oaf.LINK_PHONE,
                     oaf.LINK_EMAIL
                 from order_base_info obi
                          LEFT JOIN order_application_form oaf ON obi.ORDER_NO = oaf.ORDER_NO
        <include refid="baseQueryWhere"/>
                 UNION ALL

                 SELECT
                     '子订单',
                     obi.ORDER_ID,
                     obi.ORDER_NO as RELATE_ORDER_NO,
                     t.ORDER_NO  ,
                     t.CREATE_DATE,
        (SELECT CONFIRM_DATE from order_operator_log where OPERATOR_TEXT = t.ORDER_NO and OPERATOR_TYPE = '200' and CONFIRM_DATE is not null ORDER BY LOG_ID LIMIT 1) CONFIRM_ORDER_DATE,
                     obi.STATE,
                     obi.MONTH_PAY,
                     t.PAY_METHOD AS PAY_STATE,
                     oaf.company_NAME_CN AS COMPANY_NAME_CN,
                     t.CURRENCY,
                     t.REAL_AMOUNT,
                     obi.LAB_NAME,
                     oaf.LINK_PERSON,
                     oaf.LINK_PHONE,
                     oaf.LINK_EMAIL
                 from order_base_info t
                          LEFT JOIN order_base_info obi ON obi.ORDER_NO = t.RELATE_ORDER_NO
                          LEFT JOIN order_application_form oaf ON obi.ORDER_NO = oaf.ORDER_NO
        <include refid="baseQueryWhere"/>
             ) allOrder
        ORDER BY
            IFNULL( allOrder.CONFIRM_ORDER_DATE, allOrder.CREATE_DATE ) DESC,
            allOrder.ORDER_ID DESC


    </select>
    <select id="qryOrderNumByState" resultMap="RSTSPersonCenterResultMap">
        SELECT obi.STATE,count(obi.STATE) as STATE_COUNT from order_base_info obi
        <include refid="BaseQryWhere"/>
        GROUP BY obi.STATE
    </select>

    <select id="qryNotPayMainList" resultMap="RSTSPersonCenterResultMap">
        SELECT 60 AS 'STATE',count(*) as STATE_COUNT ,GROUP_CONCAT(obi.ORDER_NO) as ORDER_NO_STR from order_base_info obi
        <include refid="BaseQryWhere"/>
        AND obi.PAY_METHOD IS NULL
    </select>
    <select id="qryNotPaySubList" resultMap="RSTSPersonCenterResultMap">
        SELECT 60 AS 'STATE',count(*) as STATE_COUNT from order_base_info obi,order_base_info o
        <include refid="BaseQryWhere"/>
       AND  obi.order_no=o.RELATE_ORDER_NO  and o.user_id=obi.user_id
        and o.STATE not in ( '91' )
        AND o.PAY_METHOD IS NULL
    </select>


    <select id="qryAllParentDetailItemByMap" resultMap="resultDTO">
        select item_id,item_type,count(DISTINCT obi.order_no) as SALE_NUM from order_base_info obi,order_detail od where obi.order_no=od.order_no
        <include refid="qryAllParentDetailItemByMapWhere"></include>
        group by  item_id,item_type
    </select>


    <sql id="BaseQryWhere">
        where 1=1
        <if test="userId != null">
            AND obi.USER_ID=#{userId}
        </if>
        <if test="orderType != null">
            AND obi.ORDER_TYPE = #{orderType}
        </if>
        <if test="custIdList != null">
            AND obi.CUST_ID in
            <foreach collection="custIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="orderTypeList != null ">
            AND obi.ORDER_TYPE in
            <foreach collection="orderTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="notStateList != null ">
            AND obi.STATE not in
            <foreach collection="notStateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="stateList != null ">
            AND obi.STATE in
            <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderNoList != null ">
            AND obi.RELATE_ORDER_NO in
            <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="qryAllParentDetailItemByMapWhere">

        <if test="userId != null">
            AND obi.USER_ID=#{userId}
        </if>
        <if test="orderType != null">
            AND obi.ORDER_TYPE = #{orderType}
        </if>
        <if test="custIdList != null">
            AND obi.CUST_ID in
            <foreach collection="custIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="parentDetailId != null ">
            AND od.PARENT_DETAIL_ID =#{parentDetailId}
        </if>
        <if test="itemIdList != null">
            AND od.ITEM_ID in
            <foreach collection="itemIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>

        <if test="stateList != null ">
            AND obi.STATE in
            <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="qryOrderNumByStateWhere">
        WHERE 1=1
        <if test="userId != null">
            AND obi.USER_ID=#{userId}
        </if>
        <if test="orderTypeList != null ">
            AND obi.ORDER_TYPE in
            <foreach collection="orderTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="custIdList != null">
            and obi.CUST_ID in
            <foreach collection="custIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="rstsNotStateList != null ">
            AND obi.STATE not in
            <foreach collection="rstsNotStateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </sql>

    <select id="selectNewListCountByReq" resultType="java.lang.Integer">
        select count(1)
        from ORDER_BASE_INFO obi
        <include refid="baseNewQueryWhere"/>
        <include refid="includeNewQueryWhere"/>
    </select>

    <select id="qryListByMap" resultMap="resultDTO">
        select
        <include refid="sqlListDTO"/>
        from ORDER_BASE_INFO obi
        <include refid="BaseQryWhere"/>
        order by obi.CREATE_DATE desc
        <include refid="useLimit"/>
    </select>

    <select id="qryList" resultMap="resultDTO">
        select
        <include refid="sqlListDTO"/>
        from ORDER_BASE_INFO obi
        <include refid="BaseQryListWhere"/>
        order by obi.CREATE_DATE desc
    </select>

    <sql id="BaseQryListWhere">
        where 1=1
        <if test="orderNoList != null ">
            AND obi.ORDER_NO in
            <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderId != null ">
            AND obi.ORDER_ID =#{orderId}
        </if>
    </sql>


    <sql id="includeNewQueryWhere">
        <if test="useOrderTypeQry == 200000">
            <include refid="inquiryQueryWhere"/>
        </if>
        <if test="useOrderTypeQry == 210000">
            <include refid="orderQueryWhere"/>
        </if>
    </sql>

    <sql id="baseNewQueryWhere">
        where 1=1
        <if test="userId != null">
            AND obi.USER_ID=#{userId}
        </if>
        <if test="orderTypeList != null ">
            AND obi.ORDER_TYPE in
            <foreach collection="orderTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="isDelete != null">
            AND obi.IS_DELETE=#{isDelete}
        </if>

        <if test="subStateNot != null">
            AND obi.SUB_STATE!=#{subStateNot}
        </if>

        <if test="bu != null and bu !=''">
            AND obi.BU=#{bu}
        </if>
        <if test="lineId != null">
            AND obi.LINE_ID=#{lineId}
        </if>
        <if test="stateNot != null ">
            AND obi.STATE !=#{stateNot}
        </if>
        <if test="inquiryConfirmFlg != null ">
            <if test="inquiryConfirmFlg ==1">
                AND obi.state!=91 and obi.RELATE_ORDER_NO in (select oo.ORDER_NO  from ORDER_BASE_INFO oo where oo.state=4 and oo.order_type in (200000,200001)
                <if test="userId != null">
                    AND oo.USER_ID=#{userId}
                </if>
                )
            </if>
            <if test="inquiryConfirmFlg ==0">
                AND obi.state!=91 AND obi.RELATE_ORDER_NO  in (select oo.ORDER_NO  from ORDER_BASE_INFO oo where oo.state=3 and oo.order_type in (200000,200001)
                <if test="userId != null">
                    AND oo.USER_ID=#{userId}
                </if>
                )
            </if>
        </if>

        <if test="stateList != null and  stateList.size>0 ">
            AND
            <foreach collection="stateList" index="index" item="item" open="(" separator=" or " close=")">
                <if test=" item ==70 ">
                    obi.SUB_STATE=70
                </if>
                <if test=" item != 70 ">
                    obi.STATE=#{item}
                </if>
            </foreach>
        </if>
        <if test="createDate != null and createDate.length &gt;0">
            and obi.CREATE_DATE between
            <foreach close="" collection="createDate.split('/')" index="index" item="createDate" open="" separator="and">
                #{createDate,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="outOrderNo != null and outOrderNo != ''">
            and obi.out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="ladingNo != null and ladingNo != ''">
            and obi.ORDER_NO in (
            SELECT os.ORDER_NO FROM order_shipping os WHERE os.LADING_NO = #{ladingNo,jdbcType=VARCHAR}
            )
        </if>
        <if test="invoiceTitle != null and invoiceTitle != ''">
            and obi.ORDER_NO in (
            SELECT DISTINCT oa.ORDER_NO FROM order_attachment oa WHERE oa.ATT_TYPE = '11' AND oa.STATE = 1 AND oa.FILE_NAME LIKE concat('%',#{invoiceTitle,jdbcType=VARCHAR},'%')
            )
        </if>
    </sql>

    <sql id="inquiryQueryWhere">
        <if test="keyword != null  and keyword !=''">
            <![CDATA[
        AND (obi.ORDER_NO like concat('%',#{keyword},'%') or PRODUCT_NAME like concat('%',#{keyword},'%') or CATEGORY_PATH like concat('%',#{keyword},'%'))
          ]]>
        </if>
    </sql>
    <sql id="orderQueryWhere">
        <if test="sampleStr != null and sampleStr !=''">
            AND obi.ORDER_NO in(
            select obi.ORDER_NO
            from order_sample_from osf ,order_sample os ,order_base_info obi
            where osf.ORDER_NO=obi.ORDER_NO and osf.GROUP_NO=obi.GROUP_NO
            and osf.SAMPLE_NO=os.SAMPLE_NO
            and os.ORDER_NO=obi.ORDER_NO and os.GROUP_NO=obi.GROUP_NO
            and os.STATE=1
            and osf.STATE=1
            and obi.ORDER_TYPE in (210000,210001)
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            and (os.SAMPLE_NAME_CN like concat('%',#{sampleStr},'%')  or os.SAMPLE_NAME_EN like  concat('%',#{sampleStr},'%')
            or osf.SAMPLE_VALUE like concat('%',#{sampleStr},'%')
            )
            group by obi.ORDER_NO
            )
        </if>
        <if test="itemStr != null and itemStr !=''">
            AND ( obi.ORDER_NO in(
            select obi.ORDER_NO from  ORDER_DETAIL od ,ORDER_BASE_INFO obi
            where od.ORDER_NO=obi.ORDER_NO and od.GROUP_NO=obi.GROUP_NO
            and od.ITEM_NAME like concat('%',#{itemStr},'%')
            and obi.ORDER_TYPE in (210000,210001)
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            group by obi.ORDER_NO)
            or
            obi.RELATE_ORDER_NO in(
            select obi.ORDER_NO from  ORDER_DETAIL od ,ORDER_BASE_INFO obi
            where od.ORDER_NO=obi.ORDER_NO and od.GROUP_NO=obi.GROUP_NO
            and od.ITEM_NAME like concat('%',#{itemStr},'%')
            and obi.ORDER_TYPE in (200001)
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            group by obi.ORDER_NO)
            )
        </if>
        <if test="reportNo != null and reportNo !=''">
            AND obi.ORDER_NO in(
            select oa.ORDER_NO from ORDER_ATTACHMENT oa,ORDER_BASE_INFO obi  where oa.ATT_TYPE=10 and
            oa.FILE_NAME like  concat('%',#{reportNo},'%')
            and obi.ORDER_TYPE in (210000,210001) and obi.ORDER_NO=oa.ORDER_NO
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            )
        </if>
        <if test="applicationCompanyNameStr != null and applicationCompanyNameStr !=''">
            AND obi.ORDER_NO in(
            select oaf.ORDER_NO from ORDER_APPLICATION_FORM oaf,ORDER_BASE_INFO obii
            where obii.ORDER_TYPE in (210000,210001) and oaf.ORDER_NO=obii.ORDER_NO
            and (oaf.COMPANY_NAME_CN like concat('%',#{applicationCompanyNameStr},'%') or oaf.COMPANY_NAME_EN like concat('%',#{applicationCompanyNameStr},'%'))
            )
        </if>

        <if test="reportCompanyNameStr != null and reportCompanyNameStr !=''">
            AND obi.ORDER_NO in(
            select oaf.ORDER_NO from ORDER_REPORT oaf,ORDER_BASE_INFO obii
            where obii.ORDER_TYPE in (210000,210001) and oaf.ORDER_NO=obii.ORDER_NO
            and (oaf.REPORT_COMPANY_NAME_CN like concat('%',#{reportCompanyNameStr},'%') or oaf.REPORT_COMPANY_NAME_EN like concat('%',#{reportCompanyNameStr},'%'))
            )
        </if>
        <if test="keywordOrderNo != null and keywordOrderNo !=''">
            <![CDATA[
          AND (
          obi.ORDER_NO like concat('%',#{keywordOrderNo},'%') or
          obi.ORDER_NO in (select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=211000 and ORDER_NO like concat('%',#{keywordOrderNo},'%'))
        )
         ]]>
        </if>
        <if test="keyword != null and keyword !=''">
            AND (
            obi.ORDER_NO like concat('%',#{keyword},'%') or
            obi.ORDER_NO in(
            select obi.ORDER_NO
            from order_sample_from osf ,order_sample os ,order_base_info obi
            where osf.ORDER_NO=obi.ORDER_NO and osf.GROUP_NO=obi.GROUP_NO
            and osf.SAMPLE_NO=os.SAMPLE_NO
            and os.ORDER_NO=obi.ORDER_NO and os.GROUP_NO=obi.GROUP_NO
            and os.STATE=1
            and obi.ORDER_TYPE in (210000,210001)
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            and (os.SAMPLE_NAME_CN like concat('%',#{keyword},'%') or os.SAMPLE_NAME_EN like
            concat('%',#{keyword},'%')
            or osf.SAMPLE_VALUE like concat('%',#{keyword},'%')
            )
            group by obi.ORDER_NO
            union all
            select oaf.ORDER_NO from ORDER_REPORT oaf,ORDER_BASE_INFO obii
            where obii.ORDER_TYPE in (210000,210001) and oaf.ORDER_NO=obii.ORDER_NO
            and (oaf.REPORT_COMPANY_NAME_CN like concat('%',#{keyword},'%') or oaf.REPORT_COMPANY_NAME_EN like concat('%',#{keyword},'%'))
            <if test="userId != null">
                AND obii.USER_ID=#{userId}
            </if>
            union all
            select obi.ORDER_NO from ORDER_DETAIL od ,ORDER_BASE_INFO obi
            where od.ORDER_NO=obi.ORDER_NO and od.GROUP_NO=obi.GROUP_NO
            and od.ITEM_NAME like concat('%',#{keyword},'%')
            and obi.ORDER_TYPE in (210000,210001)
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
            group by obi.ORDER_NO
            union all
            select RELATE_ORDER_NO as ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=211000 and ORDER_NO like concat('%',#{keyword},'%')
            ))
        </if>

        <if test="finishDate != null and finishDate.length &gt;0">
            AND obi.STATE=80 AND obi.STATE_DATE  between
            <foreach close="" collection="finishDate.split('/')" index="index" item="finishDate" open="" separator="and">
                #{finishDate,jdbcType=VARCHAR}
            </foreach>
        </if>

        <if test="useCustomerUserId != null">
        and obi.order_no in (
        select distinct ORDER_NO from (
        select order_no from ORDER_CUSTOMER oc where (OBJECT_TYPE,OBJECT_ID) in (SELECT "CUST",cust_id FROM tb_cust_apply_relate tcar
        where tcar.user_id=#{useCustomerUserId} and tcar.bu="901" and tcar.state=1) and oc.state=1
        union all
        select ORDER_NO  from order_customer oc where oc.OBJECT_TYPE="user" and oc.OBJECT_ID=#{useCustomerUserId} and oc.state=1) t
        ) and ((obi.state!=11 and obi.state!=91) or (obi.state=91 and obi.his_state!=11))
        </if>
    </sql>
    <sql id="roQueryWhere">
        <if test="custIdList != null">
            and obi.CUST_ID in
            <foreach collection="custIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="roPayState != null">
            <if test="roPayState==0">
                and (obi.PAY_METHOD is null or obi.ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO
                where ORDER_TYPE=301000 and PAY_METHOD is null) )
            </if>
            <if test="roPayState==1">
                and (obi.PAY_METHOD is not null or obi.ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO
                where ORDER_TYPE=301000 and PAY_METHOD is not null) )
            </if>
        </if>
    </sql>
    <sql id="ticQueryWhere">
        <if test="monthPay != null and orderType == 100000" >
            <if test="monthPay ==0">
                AND obi.MONTH_PAY=0
            </if>
            <if test="monthPay ==2">
                AND obi.MONTH_PAY in(1,3)
            </if>
        </if>
        <if test="downReport != null">
            <if test="downReport == 0">
                AND obi.ORDER_NO not in(
                select DISTINCT ORDER_NO from ORDER_ATTACHMENT where
                obi.ORDER_NO not in(
                select DISTINCT ORDER_NO from ORDER_ATTACHMENT where ATT_TYPE=10 and DOWN_LOAD=0
                ) and ATT_TYPE=10
                )
            </if>
            <if test="downReport == 1">
                AND obi.ORDER_NO in(
                select DISTINCT ORDER_NO from ORDER_ATTACHMENT where
                ORDER_NO not in(
                select DISTINCT ORDER_NO from ORDER_ATTACHMENT where ATT_TYPE=10 and DOWN_LOAD=0
                ) and ATT_TYPE=10
                )
            </if>
        </if>
    </sql>



    <resultMap id="baseStateNumMap" type="com.sgs.ecom.member.entity.extend.OrderBaseInfoExtend" >
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="NUM" property="num" jdbcType="INTEGER" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectOrderBaseStateNumByVO"  resultMap="baseStateNumMap"  >
        select obi.STATE ,obi.ORDER_TYPE ,count(1) as NUM from ORDER_BASE_INFO obi
        where obi.USER_ID=#{userId} and IS_DELETE=0  and obi.state!=3 and obi.state!=4
        <if test="bu != null">
            AND obi.BU=#{bu}
        </if>
        group by obi.state,obi.ORDER_TYPE
        union all
        select  obi.STATE ,obi.ORDER_TYPE ,count(1) as NUM from ORDER_BASE_INFO obi where obi.USER_ID=#{userId} and obi.IS_DELETE=0
        <if test="bu != null">
            AND obi.BU=#{bu}
        </if>
        and obi.state in (3,4) and obi.order_type=200000 group by obi.state,obi.ORDER_TYPE
        union all
        select  obi.STATE ,obi.ORDER_TYPE ,count(1) as NUM from ORDER_BASE_INFO obi,order_base_info obi2
        where obi.USER_ID=#{userId} and obi.IS_DELETE=0 and obi.state in (3,4)  and obi.order_type=200001
        <if test="bu != null">
            AND obi.BU=#{bu}
        </if>
        and obi.ORDER_NO=obi2.RELATE_ORDER_NO and obi2.state!=91
        group by obi.state,obi.ORDER_TYPE
        union all
        select 10 as state,"210000" as ORDER_TYPE,count(1) from (
        select obi.order_type,obi.bu from order_base_info obi  where obi.ORDER_TYPE=210000  and obi.PAY_STATE=0 and obi.STATE!=91 and obi.USER_ID=#{userId}
        union all
        select obi.ORDER_TYPE,(select bu from order_base_info obi2 where obi2.order_no=obi.RELATE_ORDER_NO) from ORDER_BASE_INFO obi
        where obi.ORDER_TYPE=211000   and obi.PAY_STATE=0 and obi.STATE!=91 and obi.USER_ID=#{userId}
        ) t
        <where>
            <if test="bu != null">
                AND t.BU=#{bu}
            </if>
        </where>
        union all
        select 10 as state,"210001" as ORDER_TYPE,count(1) from (
        select obi.order_type,obi.bu from
        ORDER_BASE_INFO obi,ORDER_BASE_INFO obii where obi.RELATE_ORDER_NO=obii.ORDER_NO  and obii.state in (3,4)
        and obi.user_id=#{userId} and obi.PAY_STATE =0  and obi.ORDER_TYPE=210001 and obi.state!=91
        ) t1
        <where>
            <if test="bu != null">
                AND t1.BU=#{bu}
            </if>
        </where>
    </select>

    <resultMap id="baseTodoMap" type="com.sgs.ecom.member.entity.extend.OrderBaseInfoExtend" >
        <result column="num" property="sortNum" jdbcType="VARCHAR" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER" />
    </resultMap>

    <select id="qryOrderToDoEntityByVO"  resultMap="baseTodoMap"  >
        select t.num,t.ORDER_NO,t.CREATE_DATE,t.REAL_AMOUNT,t.CURRENCY,t.ORDER_TYPE  from (
        <if test="orderFlg==1">
        select  "1" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE  from  order_base_info obi
        where obi.state=3 and obi.user_id=#{userId} AND obi.ORDER_TYPE="200000"
        union all
        select "1.1" as num,(select ORDER_NO from ORDER_BASE_INFO oo where oo.RELATE_ORDER_NO=obi.ORDER_NO order by oo.order_id desc limit 1) ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE  from  order_base_info obi
        where obi.state=3 and obi.user_id=#{userId} AND obi.ORDER_TYPE="200001"
        union all
        select "2" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE from order_base_info obi
        where obi.user_id=#{userId} and obi.PAY_STATE =0 and DATE_ADD(obi.CREATE_DATE,INTERVAL 1 DAY) &lt; now() and obi.ORDER_TYPE=210000 and obi.state!=91
        union all
        select "2.2" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE from
        ORDER_BASE_INFO obi,ORDER_BASE_INFO obii where obi.RELATE_ORDER_NO=obii.ORDER_NO  and obii.state=4
        and obi.user_id=#{userId} and obi.PAY_STATE =0 and DATE_ADD(obii.STATE_DATE,INTERVAL 1 DAY) &lt; now() and obi.ORDER_TYPE=210001 and obi.state!=91
        union all
        select "3" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE  from order_base_info obi
        where obi.user_id=#{userId}  and DATE_ADD(obi.CREATE_DATE,INTERVAL 1 DAY) &lt; now() and obi.state=11 and obi.SUB_STATE!=70 and obi.ORDER_TYPE=210000
        union all
        select "4" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE  from order_base_info obi
        where obi.user_id=#{userId}  and obi.SUB_STATE =70 and obi.ORDER_TYPE=210000
        union all
        select "5" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE  from order_base_info obi
        where obi.user_id=#{userId} and obi.state=12 and obi.ORDER_TYPE in (210000,210001)
        union all
        select  "6" as num, obi.RELATE_ORDER_NO ,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,(select bu from order_base_info obi2 where obi2.order_no=obi.RELATE_ORDER_NO),
         obi.ORDER_TYPE  from order_base_info obi
         where obi.order_type=211000 and obi.PAY_STATE =0 and obi.state!=91 and obi.user_id=#{userId}
        </if>
        <if test="storeFlg==1 and orderFlg==1">
         union all
        </if>
        <if test="storeFlg==1">
        select "11" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE from ORDER_BASE_INFO obi
        where obi.user_id=#{userId} and obi.PAY_STATE =0 and DATE_ADD(obi.CREATE_DATE,INTERVAL 1 DAY) &lt; now()
          and obi.ORDER_TYPE=100000 and obi.STATE=10  and  obi.IS_DELETE=0
        union all
        select "12" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE  from ORDER_BASE_INFO obi
        where obi.user_id=#{userId}  and DATE_ADD(obi.PAY_DATE,INTERVAL 1 DAY) &lt; now() and obi.state=11  and  obi.IS_DELETE=0
            and obi.SUB_STATE!=70 and obi.ORDER_TYPE=100000
        union all
        select "13" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE  from ORDER_BASE_INFO obi
        where obi.user_id=#{userId}  and obi.SUB_STATE =70 and obi.ORDER_TYPE=100000  and  obi.IS_DELETE=0
        union all
        select "14" as num,obi.ORDER_NO,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,obi.BU,obi.ORDER_TYPE  from ORDER_BASE_INFO obi
        where obi.user_id=#{userId} and obi.state=12 and  obi.ORDER_TYPE=100000  and  obi.IS_DELETE=0
        union all
        select  "15" as num, obi.RELATE_ORDER_NO ,obi.CREATE_DATE,obi.REAL_AMOUNT,obi.CURRENCY,(select bu from order_base_info obi2 where obi2.order_no=obi.RELATE_ORDER_NO),obi.ORDER_TYPE  from order_base_info obi
        where obi.order_type=101000 and obi.PAY_STATE =0 and obi.state!=91 and obi.user_id=#{userId}
        </if>
         ) as t
        <where>
            <if test="bu != null">
                AND t.BU=#{bu}
            </if>
            <if test="orderTypeList != null ">
                AND t.ORDER_TYPE in
                <foreach collection="orderTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>                                                           
        order by t.create_date desc,t.num asc
    </select>
    <select id="qryOrderBase" resultMap="BaseResultInfo">
        select
        <include refid="BaseQrySql"/>
        from ORDER_BASE_INFO obi
        where ORDER_NO=#{orderNo}
    </select>
    <select id="qrySubOrderList" resultMap="resultDTO">
        select
        <include refid="sqlListDTO"/>
        from ORDER_BASE_INFO obi
        where
            1=1
        <if test="relateOrderNoList != null ">
            AND obi.RELATE_ORDER_NO in
            <foreach collection="relateOrderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectWineListByMapList" resultMap="resultWineDTO">
        SELECT
        distinct
        ( CASE WHEN obi.PAY_DATE IS NULL  THEN NULL ELSE obi.PAY_DATE END ) as START_DATE,
        ( CASE WHEN (SELECT OPERATOR_DATE FROM ORDER_OPERATOR_LOG ool WHERE ORDER_TYPE = 100000 AND OPERATOR_TYPE = 80 AND obi.ORDER_NO = ool.ORDER_NO) IS NULL  THEN null ELSE (SELECT OPERATOR_DATE FROM ORDER_OPERATOR_LOG ool WHERE ORDER_TYPE = 100000 AND OPERATOR_TYPE = 80 AND obi.ORDER_NO = ool.ORDER_NO) END ) as END_DATE,
        obi.ORDER_NO,
        obi.STATE,
        obi.CREATE_DATE,
        op.STORE_NAME,
        obi.REAL_AMOUNT AS PRICE,
        (CASE WHEN result.HOTEL_OFFICIAL_NAME IS NOT NULL   THEN result.HOTEL_OFFICIAL_NAME ELSE (CASE WHEN obi.STATE = 10 OR obi.STATE = 11 or obi.STATE = 91  THEN obi.COMPANY_NAME ELSE result.HOTEL_OFFICIAL_NAME END) END)  AS HOTEL_OFFICIAL_NAME,
        (CASE WHEN result.HOTEL_ADDRESS IS NOT NULL  THEN result.HOTEL_ADDRESS ELSE (CASE WHEN obi.STATE = 10 OR obi.STATE = 11 or obi.STATE = 91  THEN CONCAT(obi.PROVINCE,obi.CITY,obi.TOWN,obi.COMPANY_ADDRESS_CN) ELSE result.HOTEL_ADDRESS END) END)  AS HOTEL_ADDRESS,

	result.BRAND,
        (CASE WHEN result.USER_NAME IS NOT NULL  THEN result.USER_NAME ELSE (CASE WHEN obi.STATE = 10 OR obi.STATE = 11 or obi.STATE = 91  THEN obi.USER_NAME ELSE result.USER_NAME END) END)  AS USER_NAME,
        (CASE WHEN result.USER_PHONE IS NOT NULL  THEN result.USER_PHONE ELSE (CASE WHEN obi.STATE = 10 OR obi.STATE = 11 or obi.STATE = 91  THEN obi.USER_PHONE ELSE result.USER_PHONE END) END)  AS USER_PHONE,
	result.USER_EMAIL,
	result.MANAGEMENT_TYPE,
	result.PROVINCE,
	result.WHID,
  result.HOTEL_CODE,
	result.HOTEL_AREA,
	result.HOTEL_PROVINCE,
	result.HOTEL_SUB_AREA,
	result.CITY
        FROM
        ORDER_BASE_INFO obi
        LEFT JOIN ORDER_PRODUCT op ON obi.ORDER_NO = op.ORDER_NO and op.state = 1
        LEFT JOIN (
                SELECT
                       distinct
                    oaf.ORDER_NO,
		max( ( case oaf.SAMPLE_KEY when 'hotelOfficialName' then oaf.SAMPLE_VALUE else '' end ) ) HOTEL_OFFICIAL_NAME,
		max( ( case oaf.SAMPLE_KEY when 'hotelAddress' then oaf.SAMPLE_VALUE else '' end ) ) HOTEL_ADDRESS,
		max( ( case oaf.SAMPLE_KEY when 'brand' then (case when oaf.SAMPLE_EXPLAIN is null then oaf.SAMPLE_VALUE else oaf.SAMPLE_EXPLAIN end) else '' end ) ) BRAND,
		max( ( case oaf.SAMPLE_KEY when 'brand' then oaf.SAMPLE_VALUE else '' end ) ) BRAND_CODE,
		max( ( case oaf.SAMPLE_KEY when 'contactName' then oaf.SAMPLE_VALUE else '' end ) ) USER_NAME,
		max( ( case oaf.SAMPLE_KEY when 'telephone' then oaf.SAMPLE_VALUE else '' end ) ) USER_PHONE,
		max( ( case oaf.SAMPLE_KEY when 'email' then oaf.SAMPLE_VALUE else '' end ) ) USER_EMAIL,
		max( ( case oaf.SAMPLE_KEY when 'managementType' then (case when oaf.SAMPLE_EXPLAIN is null then oaf.SAMPLE_VALUE else oaf.SAMPLE_EXPLAIN end) else '' end ) ) MANAGEMENT_TYPE,
		max( ( case oaf.SAMPLE_KEY when 'managementType' then oaf.SAMPLE_VALUE else '' end ) ) MANAGEMENT_TYPE_CODE,
		max( ( case oaf.SAMPLE_KEY when 'province' then oaf.SAMPLE_VALUE else '' end ) ) PROVINCE,
		max( ( case oaf.SAMPLE_KEY when 'whid' then oaf.SAMPLE_VALUE else '' end ) ) WHID,
		max( ( case oaf.SAMPLE_KEY when 'hotelCode' then oaf.SAMPLE_VALUE else '' end ) ) HOTEL_CODE,
		max( ( case oaf.SAMPLE_KEY when 'hotelArea' then oaf.SAMPLE_VALUE else '' end ) ) HOTEL_AREA,
		max( ( case oaf.SAMPLE_KEY when 'hotelProvince' then oaf.SAMPLE_VALUE else '' end ) ) HOTEL_PROVINCE,
		max( ( case oaf.SAMPLE_KEY when 'hotelSubArea' then oaf.SAMPLE_VALUE else '' end ) ) HOTEL_SUB_AREA,
		max( ( case oaf.SAMPLE_KEY when 'city' then oaf.SAMPLE_VALUE else '' end ) ) CITY
                FROM

                     ORDER_PRODUCT op,
                  order_base_info obii,
                    <if test="finishDate != null  and finishDate ==1 ">
                        ORDER_OPERATOR_LOG ool,
                    </if>
                        ORDER_SAMPLE_FROM oaf
                WHERE
                    obii.ORDER_NO = op.ORDER_NO
        AND obii.ORDER_NO = oaf.ORDER_NO
        AND   obii.ORDER_TYPE = 100000
                    AND obii.IS_TEST = 0
        and op.state = 1
        <if test="finishDate != null and finishDate ==1 ">
                        AND ool.ORDER_NO = oaf.ORDER_NO
                        <if test="startDate != null and startDate !='' ">
                            AND DATE_FORMAT(ool.OPERATOR_DATE,'%Y-%m-%d %H:%i:%s') &gt;=  #{startDate}
                        </if>
                        <if test="endDate != null and endDate !='' ">
                            AND DATE_FORMAT(ool.OPERATOR_DATE,'%Y-%m-%d %H:%i:%s') &lt;=  #{endDate}
                        </if>
                        AND  ool.OPERATOR_TYPE = 80
                    </if>
                    <if test="ProdIdList != null ">
                        AND  op.PROD_ID in
                        <foreach collection="ProdIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                  AND oaf.STATE = 1
                GROUP BY oaf.ORDER_NO
            ) result ON obi.ORDER_NO = result.ORDER_NO
                    <if test="finishDate != null and finishDate ==1 ">
                        LEFT JOIN ORDER_OPERATOR_LOG ool ON obi.ORDER_NO = ool.ORDER_NO
                    </if>

        <include refid="wineQueryWhere"/>
        ORDER BY obi.CREATE_DATE DESC
    </select>

</mapper>