package com.sgs.ecom.member.config;
import com.google.common.collect.Maps;
import com.sgs.ecom.member.enumtool.dml.BusinessCodeEnum;
import com.sgs.ecom.member.enumtool.order.PriceTypeEnum;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
*
* @return
* <AUTHOR> || created at 2024/8/29 16:16 
* @throws Exception 抛出错误    
*/
@RefreshScope
@Component
@ConfigurationProperties(prefix = "order.oiq.word")
public class WordProperties {
    /**
     * WEB SITE配置
     */

    private Map<String, Integer> quotation = Maps.newHashMap();
    private Map<String, Integer> form = Maps.newHashMap();



    public Map<String, Integer> getQuotation() {
        return quotation;
    }

    public void setQuotation(Map<String, Integer> quotation) {
        this.quotation = quotation;
    }

    public Integer getWordQuotationByPriceType(String bu,Integer priceType,Integer disCountZeroFlg){
        String priceKey=PriceTypeEnum.getName(priceType);
        String key=bu+"-"+priceKey;
        if(disCountZeroFlg!=null &&disCountZeroFlg==1){
            key=bu+"-"+priceKey+"-ZERO";
        }
        return quotation.getOrDefault(key,null);
    }
    public Integer getMinQuotation(String platformCode){
        return quotation.getOrDefault(platformCode,null);
    }


    public Integer getWordFormByForeign(String businessCode, int foreign) {
        String type = foreign == 1 ? "foreign" : "DEFAULT";
        if (BusinessCodeEnum.MIN_EMS_KJ.getName().equals(businessCode)) {
            type = foreign == 1 ? "MIN-EMS-KJ-FOREIGN" : "MIN-EMS-KJ";
        }
        return form.getOrDefault(type, form.getOrDefault("DEFAULT", null));
    }

    public Map<String, Integer> getForm() {
        return form;
    }

    public void setForm(Map<String, Integer> form) {
        this.form = form;
    }
}
