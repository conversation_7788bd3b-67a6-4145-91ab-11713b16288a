package com.sgs.ecom.member.dto.base;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

import java.math.BigDecimal;

public class BaseOrderDTO extends BaseOrderFilter{
	private Long orderId;
	@ApiAnno(groups={OrderApplicationOther.class,WordQuotation.class,WordForm.class})
	private String orderNo;
	private String relateOrderNo;
	private Integer isTest;
	@ApiAnno(groups={OrderToOther.class,WordQuotation.class})
	private BigDecimal realAmount;
	private String orderType;
	private Long userId;
	@ApiAnno(groups={OrderToOther.class,WordQuotation.class})
	private String csCode;
	@ApiAnno(groups={WordQuotation.class})
	private String csEmail;
	@ApiAnno(groups={WordQuotation.class})
	private String csPhone;

	@ApiAnno(groups={OrderToOther.class})
	private String businessPersonEmail;
	@ApiAnno(groups={OrderToOther.class})
	private String userName;
	private Integer userSex;
	@ApiAnno(groups={OrderApplicationOther.class,OrderToOther.class})
	private String userPhone;
	private String userEmail;
	private String recommendReason;
	private String sampleRequirements;
	private String companyName;
	private Integer state;
	private String groupNo;
	private String tmpGroupNo;
	private Long questionId;
	private String bu;
	private Long categoryId;
	private String categoryPath;
	private String createDate;
	private Integer isPayReceived;
	@ApiAnno(groups={OrderToOther.class})
	private String labName;
	private String fromSource;
	private String platformOrder;
	private BigDecimal platformAmount;
	@ApiAnno(groups={OrderToOther.class})
	private Integer isUrgent;
	private Long labId;
	private Long custId;
	private Integer monthPay;
	private Integer isElectron; //是否是上海开票显示电子 0-否 1-是
	private String operatorCode;
	private Integer hisState;
	private Integer subState;
	private Integer refundState;

	private String reportLuaCode;
	private String reportLua;
	private String reportFormCode;
	private String reportForm;
	private String payMethod;
	private Integer payState;
	@ApiAnno(groups={WordQuotation.class})
	private String currency;


	private String companyNameEn;
	private String companyAddressCn;
	private String companyAddressEn;
	private Long lineId;
	private Long applicationLineId;
	private String salesCode;
	private String salesPhone;
	private String bossNo;
	@ApiAnno(groups={OrderToOther.class,WordQuotation.class})
	private BigDecimal orderAmount;
	@ApiAnno(groups={OrderToOther.class,WordQuotation.class})
	private BigDecimal discountAmount;

	private String deadlineTime;
	@ApiAnno(groups={OrderToOther.class})
	private String applySubmitDate;
	private BigDecimal serviceAmount;
	private BigDecimal urgentAmount;
	@ApiAnno(groups={WordQuotation.class,OrderToOther.class})
	private String salesEmail;
	@ApiAnno(groups={OrderToOther.class})
	private String businessLine;
	@ApiAnno(groups={OrderToOther.class})
	private String lineCode;

	private String csName;
	private String csNameEn;

	private String outOrderNo;



	public String getCompanyNameEn() {
		return companyNameEn;
	}

	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}

	public String getCompanyAddressCn() {
		return companyAddressCn;
	}

	public void setCompanyAddressCn(String companyAddressCn) {
		this.companyAddressCn = companyAddressCn;
	}

	public String getCompanyAddressEn() {
		return companyAddressEn;
	}

	public void setCompanyAddressEn(String companyAddressEn) {
		this.companyAddressEn = companyAddressEn;
	}



	public BaseOrderDTO() {
	}

	public BaseOrderDTO(String orderNo, String orderType) {
		this.orderNo = orderNo;
		this.orderType = orderType;
	}



	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}



	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getRelateOrderNo() {
		return relateOrderNo;
	}

	public void setRelateOrderNo(String relateOrderNo) {
		this.relateOrderNo = relateOrderNo;
	}

	public String getGroupNo() {
		return groupNo;
	}

	public void setGroupNo(String groupNo) {
		this.groupNo = groupNo;
	}

	public String getTmpGroupNo() {
		return tmpGroupNo;
	}

	public void setTmpGroupNo(String tmpGroupNo) {
		this.tmpGroupNo = tmpGroupNo;
	}

	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public Integer getIsPayReceived() {
		return isPayReceived;
	}

	public void setIsPayReceived(Integer isPayReceived) {
		this.isPayReceived = isPayReceived;
	}

	public BigDecimal getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(BigDecimal realAmount) {
		this.realAmount = realAmount;
	}

	public String getUserName() {
		if(userName==null){
			return "";
		}
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserEmail() {
		if(userEmail==null){
			return "";
		}

		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getCsCode() {
		return csCode;
	}

	public void setCsCode(String csCode) {
		this.csCode = csCode;
	}

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}



	public String getUserPhone() {
		if(userPhone==null){
			return "";
		}

		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getFromSource() {
		return fromSource;
	}

	public void setFromSource(String fromSource) {
		this.fromSource = fromSource;
	}

	public String getPlatformOrder() {
		return platformOrder;
	}

	public void setPlatformOrder(String platformOrder) {
		this.platformOrder = platformOrder;
	}

	public Integer getIsUrgent() {
		return isUrgent;
	}

	public void setIsUrgent(Integer isUrgent) {
		this.isUrgent = isUrgent;
	}

	public Long getLabId() {
		return labId;
	}

	public void setLabId(Long labId) {
		this.labId = labId;
	}

	public String getCsEmail() {
		return csEmail;
	}

	public void setCsEmail(String csEmail) {
		this.csEmail = csEmail;
	}

	public String getBusinessPersonEmail() {
		return businessPersonEmail;
	}

	public void setBusinessPersonEmail(String businessPersonEmail) {
		this.businessPersonEmail = businessPersonEmail;
	}

	public Long getCustId() {
		return custId;
	}

	public void setCustId(Long custId) {
		this.custId = custId;
	}

	public Integer getMonthPay() {
		return monthPay;
	}

	public void setSubState(Integer subState) {
		this.subState = subState;
	}

	public void setPayState(Integer payState) {
		this.payState = payState;
	}

	public String getOperatorCode() {
		return operatorCode;
	}

	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}





	public String getReportLuaCode() {
		return reportLuaCode;
	}

	public void setReportLuaCode(String reportLuaCode) {
		this.reportLuaCode = reportLuaCode;
	}

	public String getReportLua() {
		return reportLua;
	}

	public void setReportLua(String reportLua) {
		this.reportLua = reportLua;
	}

	public String getReportFormCode() {
		return reportFormCode;
	}

	public void setReportFormCode(String reportFormCode) {
		this.reportFormCode = reportFormCode;
	}

	public String getReportForm() {
		return reportForm;
	}

	public void setReportForm(String reportForm) {
		this.reportForm = reportForm;
	}

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}





	public BigDecimal getPlatformAmount() {
		return platformAmount;
	}

	public void setPlatformAmount(BigDecimal platformAmount) {
		this.platformAmount = platformAmount;
	}

	public String getRecommendReason() {
		return recommendReason;
	}

	public void setRecommendReason(String recommendReason) {
		this.recommendReason = recommendReason;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public Long getLineId() {
		return lineId;
	}

	public void setLineId(Long lineId) {
		this.lineId = lineId;
	}

	public Long getApplicationLineId() {
		return applicationLineId;
	}

	public void setApplicationLineId(Long applicationLineId) {
		this.applicationLineId = applicationLineId;
	}

	public String getSampleRequirements() {
		return sampleRequirements;
	}

	public void setSampleRequirements(String sampleRequirements) {
		this.sampleRequirements = sampleRequirements;
	}

	public String getSalesCode() {
		return salesCode;
	}

	public void setSalesCode(String salesCode) {
		this.salesCode = salesCode;
	}

	public String getSalesPhone() {
		return salesPhone;
	}

	public void setSalesPhone(String salesPhone) {
		this.salesPhone = salesPhone;
	}

	public String getBossNo() {
		return bossNo;
	}

	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}

	public void setIsTest(Integer isTest) {
		this.isTest = isTest;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public void setMonthPay(Integer monthPay) {
		this.monthPay = monthPay;
	}



	public Integer getHisState() {
		return hisState;
	}

	public void setHisState(Integer hisState) {
		this.hisState = hisState;
	}

	public Integer getIsTest() {
		return isTest;
	}

	public Integer getState() {
		return state;
	}

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	public Integer getSubState() {
		return subState;
	}

	public Integer getPayState() {
		return payState;
	}

	public Integer getUserSex() {
		return userSex;
	}

	public void setUserSex(Integer userSex) {
		this.userSex = userSex;
	}

	public Integer getRefundState() {
		return refundState;
	}

	public void setRefundState(Integer refundState) {
		this.refundState = refundState;
	}

	public String getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(String deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public String getApplySubmitDate() {
		return applySubmitDate;
	}

	public void setApplySubmitDate(String applySubmitDate) {
		this.applySubmitDate = applySubmitDate;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public BigDecimal getServiceAmount() {
		return serviceAmount;
	}

	public void setServiceAmount(BigDecimal serviceAmount) {
		this.serviceAmount = serviceAmount;
	}

	public BigDecimal getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(BigDecimal urgentAmount) {
		this.urgentAmount = urgentAmount;
	}

	public String getSalesEmail() {
		return salesEmail;
	}

	public void setSalesEmail(String salesEmail) {
		this.salesEmail = salesEmail;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getCsPhone() {
		return csPhone;
	}

	public void setCsPhone(String csPhone) {
		this.csPhone = csPhone;
	}

	public String getCsName() {
		return csName;
	}

	public void setCsName(String csName) {
		this.csName = csName;
	}

	public String getCsNameEn() {
		return csNameEn;
	}

	public void setCsNameEn(String csNameEn) {
		this.csNameEn = csNameEn;
	}

	public String getOutOrderNo() {
		return outOrderNo;
	}

	public void setOutOrderNo(String outOrderNo) {
		this.outOrderNo = outOrderNo;
	}


}
