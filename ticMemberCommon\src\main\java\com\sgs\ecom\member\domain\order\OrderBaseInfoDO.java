package com.sgs.ecom.member.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.center.EnumDTO;
import com.sgs.ecom.member.dto.custom.SysPersonDTO;
import com.sgs.ecom.member.dto.dml.DmlMainReqDTO;
import com.sgs.ecom.member.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.member.dto.order.OrderBaseStateNumDTO;
import com.sgs.ecom.member.dto.order.TicGroupProduct;
import com.sgs.ecom.member.dto.rsts.CustBossDTO;
import com.sgs.ecom.member.dto.user.UserCenterStateDTO;
import com.sgs.ecom.member.dto.user.UserCompanyDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.entity.order.OrderBaseInfo;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.ResultEnumCode;
import com.sgs.ecom.member.enumtool.bbc.BbcStateEnum;
import com.sgs.ecom.member.enumtool.user.UserCenterStateEnum;
import com.sgs.ecom.member.request.oiq.OiqApplicationReq;
import com.sgs.ecom.member.request.oiq.OiqOrderBaseReq;
import com.sgs.ecom.member.request.oiq.OiqOrderReq;
import com.sgs.ecom.member.request.tic.TicOrderSaveReq;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.check.CheckUtil;
import com.sgs.ecom.member.util.order.UseDateUtil;
import com.sgs.ecom.member.util.select.SelectMapUtil;
import com.sgs.ecom.member.vo.VOOrderBaseInfo;
import com.sgs.ecom.member.vo.order.OrderToDoVO;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


public class OrderBaseInfoDO extends OrderBaseInfo {


    private BaseCopyObj baseCopy = new BaseCopyObj();

    public static void checkUser(BaseOrderDTO baseOrderDTO, UserDTO userDTO){
        if(!CheckUtil.isSame(userDTO.getUserId(),baseOrderDTO.getUserId())){
            throw new BusinessException(ResultEnumCode.USER_IS_NULL);
        }
    }



    //小门户使用 给订单添加数据
    public OrderBaseInfo addOrderByApplication(OiqOrderReqDTO oiqOrderReqDTO, OrderTypeEnum orderTypeEnum){
        OiqOrderBaseReq orderBaseReq=oiqOrderReqDTO.getOiqOrderReq().getOrderBase();
        OrderBaseInfo orderBaseInfo = init(orderBaseReq,oiqOrderReqDTO,orderTypeEnum);
        String orderNo=(orderTypeEnum==OrderTypeEnum.OIQ_PORTAL_INQUIRY)?oiqOrderReqDTO.getInquiryOrderNo():oiqOrderReqDTO.getOrderNo();
        add(orderBaseInfo,orderNo,oiqOrderReqDTO.getUserDTO(),oiqOrderReqDTO.getDateStr());
        orderBaseInfo.setOrderType(Integer.parseInt(orderTypeEnum.getIndex()));
        orderBaseInfo.setIsTest(oiqOrderReqDTO.getUserDTO().getIsTest());
        int state=(orderTypeEnum==OrderTypeEnum.OIQ_PORTAL_INQUIRY)? BaseOrderStateEnum.DISTRIBUTION.getIndex():
                oiqOrderReqDTO.getOiqOrderReq().getType()==0? BaseOrderStateEnum.WAITAPPLY.getIndex(): BaseOrderStateEnum.WAITSEND.getIndex();
        if(orderTypeEnum==OrderTypeEnum.OIQ_PORTAL_ORDER && "2700".equals(oiqOrderReqDTO.getOiqOrderReq().getOrderBase().getBu())
                && oiqOrderReqDTO.getOiqOrderReq().getType()!=0){
            state= BaseOrderStateEnum.WAITEXAMINE.getIndex();
        }

        orderBaseInfo.setState(state);

        orderBaseInfo.setUserPhone(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkPhone());
        orderBaseInfo.setUserName(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkPerson());
        orderBaseInfo.setUserEmail(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkEmail());
        orderBaseInfo.setLabId(oiqOrderReqDTO.getLabDTO().getLabId());
        return orderBaseInfo;
    }




    //移动端使用 后期都使用
    public void companyInfo(OiqOrderReq oiqOrderReq, OrderBaseInfo orderBaseInfo) {
        OiqApplicationReq application = oiqOrderReq.getApplication();
        orderBaseInfo.setCompanyName(ValidationUtil.isEmpty(application)?"":application.getCompanyNameCn());
        orderBaseInfo.setCompanyNameEn(ValidationUtil.isEmpty(application)?"":application.getCompanyNameEn());
        orderBaseInfo.setCompanyAddressCn(ValidationUtil.isEmpty(application)?"":application.getCompanyAddressCn());
        orderBaseInfo.setCompanyAddressEn(ValidationUtil.isEmpty(application)?"":application.getCompanyAddressEn());
    }



    //移动端使用 更新只有订单
    public OrderBaseInfo updateOrderByApplication(OiqOrderReqDTO oiqOrderReqDTO, OrderTypeEnum orderTypeEnum){
        OiqOrderBaseReq oiqOrderBaseReq=oiqOrderReqDTO.getOiqOrderReq().getOrderBase();
        OrderBaseInfo orderBaseInfo = init(oiqOrderBaseReq,oiqOrderReqDTO,orderTypeEnum);
        Long orderId=(orderTypeEnum==OrderTypeEnum.OIQ_PORTAL_INQUIRY)?oiqOrderReqDTO.getInquiryOrderId():oiqOrderReqDTO.getOrderDTO().getOrderId();
        String orderNo=(orderTypeEnum==OrderTypeEnum.OIQ_PORTAL_INQUIRY)?oiqOrderReqDTO.getInquiryOrderNo():oiqOrderReqDTO.getOrderNo();
        orderBaseInfo.setOrderNo(orderNo);
        orderBaseInfo.setIsTest(oiqOrderReqDTO.getUserDTO().getIsTest());
        if(orderTypeEnum==OrderTypeEnum.OIQ_PORTAL_ORDER && (oiqOrderReqDTO.getOiqOrderReq().getType()==1||
                oiqOrderReqDTO.getOiqOrderReq().getType()==2)){
            orderBaseInfo.setState( BaseOrderStateEnum.WAITSEND.getIndex());
        }
        if(orderTypeEnum==OrderTypeEnum.OIQ_PORTAL_ORDER && "2700".equals(oiqOrderReqDTO.getOiqOrderReq().getOrderBase().getBu())
                && oiqOrderReqDTO.getOiqOrderReq().getType()!=0){
            orderBaseInfo.setState( BaseOrderStateEnum.WAITEXAMINE.getIndex());
        }


        update(orderBaseInfo,orderId,oiqOrderReqDTO.getDateStr());

        orderBaseInfo.setUserPhone(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkPhone());
        orderBaseInfo.setUserName(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkPerson());
        orderBaseInfo.setUserEmail(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkEmail());
        orderBaseInfo.setLabId(oiqOrderReqDTO.getLabDTO().getLabId());
        return orderBaseInfo;
    }




    //移动端使用 后期都使用
    public void oiqOrderToDml(OiqOrderReqDTO oiqOrderReqDTO, OrderBaseInfo orderBaseInfo){
        BaseOrderDTO baseOrderDTO=new BaseOrderDTO();
        baseCopy.copyWithNull(baseOrderDTO,orderBaseInfo);
        DmlMainReqDTO dmlMainReqDTO=oiqOrderReqDTO.getDmlMainReqDTO();

        baseOrderDTO.setLineCode(oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode());
        baseOrderDTO.setBusinessLine(oiqOrderReqDTO.getBusinessLineDTO().getConfigName());
        dmlMainReqDTO.setBaseOrderDTO(baseOrderDTO);
        dmlMainReqDTO.setOwnerEmail(baseOrderDTO.getCsEmail());
    }

    //移动端使用 后期都使用
    private OrderBaseInfo init(OiqOrderBaseReq orderBaseReq, OiqOrderReqDTO oiqOrderReqDTO, OrderTypeEnum orderTypeEnum) {
        OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
        baseCopy.copyWithNull(orderBaseInfo,orderBaseReq);
        if(!ValidationUtil.isEmpty(oiqOrderReqDTO.getBusinessLineDTO())){
            orderBaseInfo.setLineId(oiqOrderReqDTO.getBusinessLineDTO().getConfigId());
            orderBaseInfo.setBusinessLine(oiqOrderReqDTO.getBusinessLineDTO().getConfigName());
        }
        // 申请表业务线
        orderBaseInfo.setApplicationLineId(oiqOrderReqDTO.getApplicationLineId());
        if(!ValidationUtil.isEmpty(oiqOrderReqDTO.getLabDTO())){
            orderBaseInfo.setLabName(oiqOrderReqDTO.getLabDTO().getLabName());
        }
        orderBaseInfo.setGroupNo(orderTypeEnum==OrderTypeEnum.OIQ_PORTAL_INQUIRY?oiqOrderReqDTO.getInquiryGroupNo():oiqOrderReqDTO.getGroupNo());
        //
        if(!ValidationUtil.isEmpty(oiqOrderReqDTO.getOrderReport())) {
            orderBaseInfo.setReportLuaCode(oiqOrderReqDTO.getOrderReport().getReportLuaCode());
            orderBaseInfo.setReportLua(oiqOrderReqDTO.getOrderReport().getReportLua());
            orderBaseInfo.setReportFormCode(oiqOrderReqDTO.getOrderReport().getReportFormCode());
            orderBaseInfo.setReportForm(oiqOrderReqDTO.getOrderReport().getReportForm());
        }

        return orderBaseInfo;
    }

    public OrderToDoVO getOrderToDoVO(ConcurrentHashMap<String,String> map,Long userId) {
        OrderToDoVO orderToDoVO= new OrderToDoVO(map);
        orderToDoVO.setBu( map.getOrDefault(SelectMapUtil.BU,null));
        orderToDoVO.setUserId(userId);
        return orderToDoVO;
    }



    private void add(OrderBaseInfo orderBaseInfo,String orderNo,UserDTO userDTO,String dateStr) {
        undelete(orderBaseInfo);
        orderBaseInfo.setUserId(userDTO.getUserId());
        orderBaseInfo.setCreateDate(dateStr);
        orderBaseInfo.setStateDate(dateStr);
        orderBaseInfo.setOrderNo(orderNo);
        orderBaseInfo.setTotalNums(1);
    }

    private void update(OrderBaseInfo orderBaseInfo,Long orderId,String dateStr) {
        undelete(orderBaseInfo);
        orderBaseInfo.setOrderId(orderId);
        orderBaseInfo.setStateDate(dateStr);
        orderBaseInfo.setTotalNums(1);
    }

    private void undelete(OrderBaseInfo orderBaseInfo) {
        orderBaseInfo.setIsDelete(0);
    }


    //ro 老的逻辑使用
    public void addBaseByBoss(VOOrderBaseInfo voOrderBaseInfo, CustBossDTO custBossDTO) {
        voOrderBaseInfo.setSalesCode(custBossDTO.getSaler());
        voOrderBaseInfo.setSalesPhone(custBossDTO.getPhone());
        voOrderBaseInfo.setBossNo(custBossDTO.getBossNo());
        voOrderBaseInfo.setCustId(custBossDTO.getCustId());
        voOrderBaseInfo.setMonthPay(custBossDTO.getMonthPay());
    }

    public static Map<Integer,Integer> getStateMapByQryMap(Map<Integer, List<OrderBaseStateNumDTO>> map, String orderType){
        if(map.containsKey(Integer.parseInt(orderType))){
            List<OrderBaseStateNumDTO> list=map.get(Integer.parseInt(orderType));
            return  list.stream().collect(Collectors.toMap(E->E.getState(), E->E.getNum(), (key1, key2) -> key2));
        }
        return new HashMap<>();
    }

    public  List<UserCenterStateDTO> qryUserCenterStateListByList(Map<Integer,List<OrderBaseStateNumDTO>> stateMap, OrderToDoVO orderToDoVO,Map<String, EnumDTO> enumDTOMap,int t) {
        Map<Integer,Integer> inquiry=getStateMapByQryMap(stateMap,OrderTypeEnum.OIQINQUIRY.getIndex());
        Map<Integer,Integer> applicationInquiry=getStateMapByQryMap(stateMap,OrderTypeEnum.OIQ_PORTAL_INQUIRY.getIndex());
        Map<Integer,Integer> order=getStateMapByQryMap(stateMap,OrderTypeEnum.OIQORDER.getIndex());
        Map<Integer,Integer> application=getStateMapByQryMap(stateMap,OrderTypeEnum.OIQ_PORTAL_ORDER.getIndex());
        Map<Integer,Integer> tic=getStateMapByQryMap(stateMap,OrderTypeEnum.TIC.getIndex());


        List<UserCenterStateDTO> returnList=new ArrayList<>();
        for(UserCenterStateEnum centerStateEnum:UserCenterStateEnum.values()){
            UserCenterStateDTO userCenterStateDTO=new UserCenterStateDTO();
            userCenterStateDTO.setState(centerStateEnum.getIndex());
            if(centerStateEnum==UserCenterStateEnum.WAIT_CONFIRM){
                userCenterStateDTO.setInquirySchemeCode(inquiry.getOrDefault(centerStateEnum.getIndex(),0));
                userCenterStateDTO.setInquiryOrderCode(applicationInquiry.getOrDefault(centerStateEnum.getIndex(),0));
            }

            if(orderToDoVO.getOrderFlg()==1 && centerStateEnum!=UserCenterStateEnum.WAIT_CONFIRM){
                int num=0;
                num=num+(orderToDoVO.getTotalInquiryFlg()==1?order.getOrDefault(centerStateEnum.getIndex(),0):0);
                num=num+(orderToDoVO.getTotalApplicationFlg()==1?application.getOrDefault(centerStateEnum.getIndex(),0):0);
                userCenterStateDTO.setInquiryOrderCode(num);
            }
            if(orderToDoVO.getStoreFlg()==1 && orderToDoVO.getTotalStoreFlg()==1){
                Integer num12 =tic.getOrDefault(12,0);
                Integer num13=tic.getOrDefault(13,0);
                if(centerStateEnum.getIndex()==12){
                    userCenterStateDTO.setShopOrderCode(num12-t);
                }else if(centerStateEnum.getIndex()==13){
                    userCenterStateDTO.setShopOrderCode(num13+num12);
                }else{
                    userCenterStateDTO.setShopOrderCode(tic.getOrDefault(centerStateEnum.getIndex(),0));
                }

            }
            userCenterStateDTO.setNum(userCenterStateDTO.getInquirySchemeCode()+userCenterStateDTO.getInquiryOrderCode()+userCenterStateDTO.getShopOrderCode());
            //设置code
            userCenterStateDTO.setStateCode("");
            if(enumDTOMap.containsKey(String.valueOf(centerStateEnum.getIndex()))){
                userCenterStateDTO.setStateCode(enumDTOMap.get(String.valueOf(centerStateEnum.getIndex())).getEnumName());
            }
            returnList.add(userCenterStateDTO);
        }
        return returnList;
    }

    public static OrderBaseInfo getOrderBaseInfoState(Long orderId,int state){
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        orderBaseInfo.setOrderId(orderId);
        orderBaseInfo.setState(state);
        orderBaseInfo.setStateDate(UseDateUtil.getDateString(new Date()));
        return orderBaseInfo;
    }


    public OrderBaseInfo initBasic(UserDTO user, String dateStr) {
        OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
        orderBaseInfo.setCreateDate(dateStr);
        orderBaseInfo.setStateDate(dateStr);
        orderBaseInfo.setIsRemind(0);
        orderBaseInfo.setIsDelete(0);
        orderBaseInfo.setIsRead(0);
        orderBaseInfo.setIsTest(0);
        orderBaseInfo.setPayState(0);

        orderBaseInfo.setUserId(user.getUserId());
        orderBaseInfo.setUserPhone(user.getUserPhone());
        orderBaseInfo.setUserSex(user.getUserSex());
        orderBaseInfo.setUserName(user.getUserName());
        if(user.getIsTest() == 1){
            orderBaseInfo.setIsTest(1);
        }
        return orderBaseInfo;
    }

    public void initOrderCustomer(OrderBaseInfo OrderBaseInfo, TicOrderSaveReq ticOrderSaveReq, UserCompanyDTO userCompanyDTO) {
        OrderBaseInfo.setUserName(ticOrderSaveReq.getLinkPerson());
        OrderBaseInfo.setUserEmail(ticOrderSaveReq.getLinkEmail());
        OrderBaseInfo.setUserPhone(ValidationUtil.isEmpty(OrderBaseInfo.getUserPhone()) ?
                ticOrderSaveReq.getLinkPhone() : OrderBaseInfo.getUserPhone());
        if (!ValidationUtil.isEmpty(userCompanyDTO)){
            OrderBaseInfo.setCompanyNameEn(userCompanyDTO.getCompanyNameEn());
            OrderBaseInfo.setCompanyAddressEn(userCompanyDTO.getCompanyAddrEn());
        }
        OrderBaseInfo.setCompanyName(ticOrderSaveReq.getCompanyNameCn());
        OrderBaseInfo.setCompanyAddressCn(ticOrderSaveReq.getCompanyAddressCn());
        OrderBaseInfo.setTown(ticOrderSaveReq.getTown());
        OrderBaseInfo.setCity(ticOrderSaveReq.getCity());
        OrderBaseInfo.setProvince(ticOrderSaveReq.getProvince());
    }

    public void initShopBasic(OrderBaseInfo OrderBaseInfo, Integer promotionState) {
        OrderBaseInfo.setOrderType(Integer.parseInt(OrderTypeEnum.TIC.getIndex()));
        OrderBaseInfo.setDiscountAmount(BigDecimal.ZERO);
        OrderBaseInfo.setState(BbcStateEnum.TO_PAY.getIndex());
        OrderBaseInfo.setMonthPay(99);
        OrderBaseInfo.setIsPayReceived(0);
        OrderBaseInfo.setProState(promotionState);
    }

    public void initShopInfo(OrderBaseInfo OrderBaseInfo, TicGroupProduct ticGroupProduct) {
        OrderBaseInfo.setBu(ticGroupProduct.getStoreId());
        OrderBaseInfo.setLabId(ticGroupProduct.getLabId());
//        OrderBaseInfo.setCatagoryId(?);
    }

    public void entityAddCsPerson(OrderBaseInfo orderBaseInfo, SysPersonDTO personDTO){
        orderBaseInfo.setCsCode(personDTO.getPersonCode());
        orderBaseInfo.setCsName(personDTO.getPersonName());
        orderBaseInfo.setCsNameEn(personDTO.getPersonNameEn());
        orderBaseInfo.setCsPhone(personDTO.getPersonPhone());
        orderBaseInfo.setCsEmail(personDTO.getPersonMail());

    }

    public void setOrderNo(OrderBaseInfo OrderBaseInfo, String orderNo) {
        OrderBaseInfo.setOrderNo(orderNo);
    }
}
