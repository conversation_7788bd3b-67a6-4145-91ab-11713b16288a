package com.sgs.ecom.member.domain.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.platform.util.ConvertUtil;
import com.platform.util.SysException;
import com.platform.util.ValidationUtil;
import com.platform.util.tools.LogUtil;
import com.sgs.base.BaseService;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.domain.cust.service.interfaces.ICustInvoiceDomainService;
import com.sgs.ecom.member.domain.order.OrderApplicationAttrDO;
import com.sgs.ecom.member.domain.order.OrderAttachmentDO;
import com.sgs.ecom.member.domain.order.OrderBaseInfoDO;
import com.sgs.ecom.member.domain.order.OrderReportDO;
import com.sgs.ecom.member.domain.order.OrderSampleDO;
import com.sgs.ecom.member.domain.order.OrderShippingContactDO;
import com.sgs.ecom.member.domain.order.bean.ShopOrderSubmit;
import com.sgs.ecom.member.domain.order.dao.OrderBaseInfoCustomMapper;
import com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper;
import com.sgs.ecom.member.domain.order.dao.OrderShippingMapper;
import com.sgs.ecom.member.domain.order.repository.interfaces.IOrderBaseInfoRepository;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderApplicationAttrDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderApplicationFormDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderAttachmentDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderCustomerDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderDetailDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderExpressDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderInvoiceDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderLabelDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderLinkDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderOperatorLogDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderPayDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderProductDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderReportDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderSampleDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderShippingContactDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderShippingDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.ISubOrderDomainService;
import com.sgs.ecom.member.domain.user.dao.OrderInvoiceMapper;
import com.sgs.ecom.member.domain.util.RoDO;
import com.sgs.ecom.member.dto.CustInfoDTO;
import com.sgs.ecom.member.dto.OrderBaseInfoCheckDTO;
import com.sgs.ecom.member.dto.OrderBaseInfoDTO;
import com.sgs.ecom.member.dto.OrderBaseInfoMoreDTO;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.dto.OrderOperatorLogDTO;
import com.sgs.ecom.member.dto.application.OrderApplicationAttrDTO;
import com.sgs.ecom.member.dto.base.BaseLog;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.bbc.BbcOrderCouponDTO;
import com.sgs.ecom.member.dto.bbc.BbcSkuAttrDTO;
import com.sgs.ecom.member.dto.center.BusinessLineDTO;
import com.sgs.ecom.member.dto.center.EnumDTO;
import com.sgs.ecom.member.dto.center.LabDTO;
import com.sgs.ecom.member.dto.cust.CustInvoiceDTO;
import com.sgs.ecom.member.dto.custom.StateAndNumDTO;
import com.sgs.ecom.member.dto.custom.SysPersonDTO;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.member.dto.detail.OrderReportDTO;
import com.sgs.ecom.member.dto.oiq.OiqOrderDTO;
import com.sgs.ecom.member.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqAddressDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqOrderInvoiceDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqUserAddressDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OrderShippingContactDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OrderShippingDTO;
import com.sgs.ecom.member.dto.order.CustomerIdDTO;
import com.sgs.ecom.member.dto.order.OrderBaseStateNumDTO;
import com.sgs.ecom.member.dto.order.OrderLabelDTO;
import com.sgs.ecom.member.dto.order.OrderLockDTO;
import com.sgs.ecom.member.dto.order.OrderProductDTO;
import com.sgs.ecom.member.dto.order.OrderSampleDTO;
import com.sgs.ecom.member.dto.order.OrderSampleStrDTO;
import com.sgs.ecom.member.dto.order.OrderSubInfoDTO;
import com.sgs.ecom.member.dto.order.OrderToDoDTO;
import com.sgs.ecom.member.dto.order.SubOrderDTO;
import com.sgs.ecom.member.dto.order.SubOrderInfoDTO;
import com.sgs.ecom.member.dto.order.TicGroupProduct;
import com.sgs.ecom.member.dto.order.ToOrderDTO;
import com.sgs.ecom.member.dto.pay.OrderBasePayDTO;
import com.sgs.ecom.member.dto.portal.PortalInquiryBaseDTO;
import com.sgs.ecom.member.dto.rsts.CustBossDTO;
import com.sgs.ecom.member.dto.rsts.RSTSSaveOrderBaseDTO;
import com.sgs.ecom.member.dto.rsts.RstsOrderDetailDTO;
import com.sgs.ecom.member.dto.rsts.RstsPersonCenterDTO;
import com.sgs.ecom.member.dto.rsts.SampleInfoDTO;
import com.sgs.ecom.member.dto.rsts.SampleItemDTO;
import com.sgs.ecom.member.dto.sample.SampleCategoryDTO;
import com.sgs.ecom.member.dto.send.CreateOrderSendDTO;
import com.sgs.ecom.member.dto.user.UserCenterStateDTO;
import com.sgs.ecom.member.dto.user.UserCompanyDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.dto.user.UserInfoDTO;
import com.sgs.ecom.member.dto.user.UserInvoiceDTO;
import com.sgs.ecom.member.entity.UserPromotion;
import com.sgs.ecom.member.entity.extend.OrderBaseInfoExtend;
import com.sgs.ecom.member.entity.order.OrderBaseInfo;
import com.sgs.ecom.member.entity.order.OrderProduct;
import com.sgs.ecom.member.enums.CreateOrderSplitType;
import com.sgs.ecom.member.enums.OrderPrintEnum;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.enumtool.OiqMailEnum;
import com.sgs.ecom.member.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.member.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.member.enumtool.OrderStateEnum;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.ResultEnumCode;
import com.sgs.ecom.member.enumtool.aplication.CertificateLuaEnum;
import com.sgs.ecom.member.enumtool.aplication.ReportLuaCodeMappingBasicEnum;
import com.sgs.ecom.member.enumtool.aplication.ReportLuaEnum;
import com.sgs.ecom.member.enumtool.bbc.AppFormCodeEnum;
import com.sgs.ecom.member.enumtool.bbc.BbcSkuAttrEnum;
import com.sgs.ecom.member.enumtool.bbc.BbcStateEnum;
import com.sgs.ecom.member.enumtool.dml.BusinessCodeEnum;
import com.sgs.ecom.member.enumtool.order.FromSourceEnum;
import com.sgs.ecom.member.enumtool.order.OrderApplicationClassifyTypeEnum;
import com.sgs.ecom.member.enumtool.order.OrderLabelCodeEnum;
import com.sgs.ecom.member.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.member.enumtool.order.OrderSubStateEnum;
import com.sgs.ecom.member.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.member.enumtool.user.UserLabelCodeEnum;
import com.sgs.ecom.member.event.EventMailUtil;
import com.sgs.ecom.member.event.EventSmsUtil;
import com.sgs.ecom.member.infrastructure.event.MailEventUtil;
import com.sgs.ecom.member.infrastructure.handle.OrderUtilHandle;
import com.sgs.ecom.member.infrastructure.rpc.CenterRespository;
import com.sgs.ecom.member.request.oiq.OiqOrderReq;
import com.sgs.ecom.member.request.oiq.OiqQryInfoReq;
import com.sgs.ecom.member.request.order.OrderApplicationAttrReq;
import com.sgs.ecom.member.request.order.OrderBaseInfoQryReq;
import com.sgs.ecom.member.request.order.OrderToDoReq;
import com.sgs.ecom.member.request.rsts.OtherCheckHabitReq;
import com.sgs.ecom.member.request.rsts.RstsApplicationReq;
import com.sgs.ecom.member.request.rsts.RstsOrderSaveReq;
import com.sgs.ecom.member.request.rsts.RstsPersonalCenterReq;
import com.sgs.ecom.member.request.tic.TicOrderReq;
import com.sgs.ecom.member.request.tic.TicOrderSaveReq;
import com.sgs.ecom.member.request.tic.TicProductReq;
import com.sgs.ecom.member.request.tic.TicStoreReq;
import com.sgs.ecom.member.request.tic.coupon.TicCouponReq;
import com.sgs.ecom.member.service.bbc.interfaces.IBBCSV;
import com.sgs.ecom.member.service.custom.interfaces.ICustInfoSV;
import com.sgs.ecom.member.service.custom.interfaces.ICustomCodeService;
import com.sgs.ecom.member.service.custom.interfaces.ICustomLimitService;
import com.sgs.ecom.member.service.order.interfaces.IOrderApplicationAttrService;
import com.sgs.ecom.member.service.order.interfaces.IOrderAttributeService;
import com.sgs.ecom.member.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.member.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.member.service.order.interfaces.IOrderDetailService;
import com.sgs.ecom.member.service.order.interfaces.IOrderInvoiceService;
import com.sgs.ecom.member.service.order.interfaces.IOrderLogService;
import com.sgs.ecom.member.service.order.interfaces.IOrderOperatorLogService;
import com.sgs.ecom.member.service.order.interfaces.IOrderSampleService;
import com.sgs.ecom.member.service.order.interfaces.ISubOrderService;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSDetailService;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSOrderUtilService;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSRedisService;
import com.sgs.ecom.member.service.template.interfaces.ICenterRestTemplateService;
import com.sgs.ecom.member.service.template.interfaces.IOpenRestTemplateService;
import com.sgs.ecom.member.service.template.interfaces.ISsoTemplateService;
import com.sgs.ecom.member.service.tic.interfaces.ITicRedisService;
import com.sgs.ecom.member.service.user.interfaces.IUserCompanyService;
import com.sgs.ecom.member.service.user.interfaces.IUserInfoService;
import com.sgs.ecom.member.service.user.interfaces.IUserLabelSV;
import com.sgs.ecom.member.service.util.interfaces.ICenterService;
import com.sgs.ecom.member.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.NumUtil;
import com.sgs.ecom.member.util.RedisUtils;
import com.sgs.ecom.member.util.ResultCode;
import com.sgs.ecom.member.util.SendMailForCsGroupUtils;
import com.sgs.ecom.member.util.check.CheckTicUtil;
import com.sgs.ecom.member.util.check.CheckUtil;
import com.sgs.ecom.member.util.collection.ListUtil;
import com.sgs.ecom.member.util.collection.StrUtil;
import com.sgs.ecom.member.util.order.OrderUtil;
import com.sgs.ecom.member.util.order.UseDateUtil;
import com.sgs.ecom.member.util.select.AttributeUtil;
import com.sgs.ecom.member.util.select.RedisKeyUtil;
import com.sgs.ecom.member.util.select.SelectBaseUtil;
import com.sgs.ecom.member.util.select.SelectListUtil;
import com.sgs.ecom.member.util.select.SelectMapUtil;
import com.sgs.ecom.member.vo.VOOrderApplicationAttr;
import com.sgs.ecom.member.vo.VOOrderApplicationForm;
import com.sgs.ecom.member.vo.VOOrderAttribute;
import com.sgs.ecom.member.vo.VOOrderBaseInfo;
import com.sgs.ecom.member.vo.VOOrderProduct;
import com.sgs.ecom.member.vo.order.OrderBaseInfoVO;
import com.sgs.ecom.member.vo.order.OrderToDoVO;
import com.sgs.redis.RedisClient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Zhang
 * @Description :
 * @date 2023/5/23
 */
@Service
public class OrderBaseInfoDomainServiceImpl extends BaseService implements IOrderBaseInfoDomainService {
    Logger logger = LoggerFactory.getLogger(OrderBaseInfoDomainServiceImpl.class);

    @Autowired
    private ICenterRestTemplateService centerRestTemplateService;
    @Autowired
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;
    @Autowired
    private CenterRespository centerRespository;
    @Autowired
    private IOrderApplicationFormDomainService orderApplicationFormDomainService;
    @Autowired
    private IOrderSampleDomainService orderSampleDomainService;
    @Autowired
    private IOrderReportDomainService orderReportDomainService;
    @Autowired
    private IOrderDetailDomainService orderDetailDomainService;
    @Autowired
    private IOrderInvoiceService orderInvoiceService;
    @Autowired
    private IOrderExpressDomainService orderExpressDomainService;
    @Resource
    private OrderBaseInfoMapper orderBaseInfoMapper;
    @Resource
    private ISsoTemplateService ssoTemplateService;
    @Resource
    private OrderBaseInfoCustomMapper orderBaseInfoCustomMapper;
    @Resource
    private IOrderInvoiceDomainService orderInvoiceDomainService;
    @Resource
    private IUserCompanyService userCompanyService;
    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private IOrderApplicationAttrDomainService orderApplicationAttrDomainService;
    @Resource
    private IOrderBaseInfoService orderBaseInfoService;
    @Resource
    private ICustomCodeService customCodeService;
    @Resource
    private IRSTSRedisService irstsRedisService;
    @Resource
    private IRSTSOrderUtilService irstsOrderUtilService;
    @Resource
    private RedisClient redisClient;
    @Resource
    private IOrderApplicationAttrService orderApplicationAttrService;
    @Resource
    private ICustInfoSV custInfoSV;
    @Resource
    private IOrderUtilService orderUtilService;
    @Resource
    private IRSTSDetailService irstsDetailService;
    @Resource
    private ISubOrderService subOrderService;

    @Resource
    private IOrderPayDomainService orderPayDomainService;
    @Resource
    private IOrderOperatorLogDomainService orderOperatorLogDomainService;
    @Resource
    private IOrderDetailService orderDetailService;
    @Resource
    private IOrderSampleService orderSampleService;
    @Resource
    private ICustInvoiceDomainService custInvoiceDomainService;
    @Resource
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;
    @Resource
    private IOrderLinkDomainService orderLinkDomainService;
    @Resource
    private IOrderBaseInfoRepository orderBaseInfoRepository;
    @Resource
    private IOrderLabelDomainService orderLabelDomainService;
    @Resource
    private IOrderAttachmentDomainService orderAttachmentDomainService;

    @Resource
    private OrderUtilHandle orderUtilHandle;
    @Value("${mail.tic.order.link}")
    private String orderLink;
    @Resource
    private IOpenRestTemplateService openRestTemplateService;
    @Autowired
    private MailEventUtil mailEventUtil;
    @Autowired
    private IOrderOperatorLogDomainService operatorLogDomainService;
    @Autowired
    private IOrderCustomerDomainService orderCustomerDomainService;
    @Autowired
    private ITicRedisService iTicRedisService;
    @Autowired
    private IBBCSV ibbcsv;
    @Resource
    private RedisUtils redisUtils;

    @Resource
    private IOrderProductDomainService orderProductDomainService;
    @Autowired
    private IOrderOperatorLogService orderOperatorLogService;
    @Autowired
    private EventMailUtil eventMailUtil;
    @Autowired
    private EventSmsUtil eventSmsUtil;
    @Resource
    private IOrderLogService iOrderLogService;
    @Autowired
    private IOrderAttributeService orderAttributeService;
    @Autowired
    private IUserLabelSV userLabelSV;
    @Autowired
    private ICustomLimitService customLimitService;
    @Resource
    private SendMailForCsGroupUtils sendMailForCsGroup;
    @Resource
    private IOrderShippingDomainService orderShippingDomainService;
    @Resource
    private IOrderShippingContactDomainService orderShippingContactDomainService;
    @Resource
    private ISubOrderDomainService subOrderDomainService;
    @Resource
    private OrderInvoiceMapper orderInvoiceMapper;
    @Resource
    private OrderShippingMapper orderShippingMapper;
    @Resource
    private ICenterService centerService;


    @Override
    public OrderBasePayDTO checkOrderIsRefund(String orderNo) {
        OrderBasePayDTO orderBasePayDTO = orderBaseInfoCustomService.selectOrderBasePayDTO(orderNo);
        //当补差价的时候 看主单是否完成
        int checkState=orderBasePayDTO.getState();
        String payMethod = orderBasePayDTO.getPayMethod();

        String orderType = orderBasePayDTO.getOrderType();
        if(OrderTypeEnum.getSub(orderType)){
            OrderBasePayDTO orderBasePayDTOMain = orderBaseInfoCustomService.selectOrderBasePayDTO(orderBasePayDTO.getRelateOrderNo());
            checkState=orderBasePayDTOMain.getState();
            payMethod = orderBasePayDTOMain.getPayMethod();
        }
        //TODO 状态改成正向判断
        if (checkState != BbcStateEnum.WAIT_APPLY.getIndex() && checkState != BbcStateEnum.WAIT_EXAMINE.getIndex() &&
                checkState != BbcStateEnum.WAIT_SEND.getIndex() )
            throw new BusinessException(ResultEnumCode.ORDER_IS_NOT_REFUND);


        int refundState = orderBasePayDTO.getRefundState();
        if (refundState == OrderRefundStateEnum.REFUND.getIndex() ||
                refundState == OrderRefundStateEnum.REFUND_CONFIRMATION.getIndex() ||
                refundState == OrderRefundStateEnum.SUCCESS.getIndex()  ||
                refundState == OrderRefundStateEnum.PART_REFUND_CONFIRMATION.getIndex())
            throw new BusinessException(ResultEnumCode.ORDER_IS_NOT_REFUND);


        int isPayReceived = orderBasePayDTO.getIsPayReceived();//是否到账
        int intValue = Integer.parseInt(orderBasePayDTO.getPayMethod());
        //线下支付.且未退款
        if(PayMethodEnum.OFFLINE.getIndex().equals(String.valueOf(intValue)) && isPayReceived == 0)
            throw new BusinessException(ResultEnumCode.ORDER_IS_NOT_REFUND);

        if(payMethod.equals(PayMethodEnum.MONTH.getIndex()))
            throw new BusinessException(ResultEnumCode.MONTH_NOT_REFUND);


        return orderBasePayDTO;
    }





    private void updateOrderBaseInfo(VOOrderBaseInfo voOrderBaseInfoUpdate) {
        orderBaseInfoMapper.updateVOByPrimaryKeySelective(voOrderBaseInfoUpdate);
    }
    @Override
    public void updateOrderBaseInfoPrivate(Long orderId) {
        VOOrderBaseInfo voOrderBaseInfoUpdate = new VOOrderBaseInfo(orderId);
        updateOrderBaseInfo(voOrderBaseInfoUpdate);
    }













    private void insertEntity(OrderBaseInfo orderBaseInfo) {
        orderBaseInfoMapper.insertEntity(orderBaseInfo);
    }

    @Override
    public void updateEntity(OrderBaseInfo orderBaseInfo) {
        if(ValidationUtil.isEmpty(orderBaseInfo)){
            return;
        }
        orderBaseInfoMapper.updateEntity(orderBaseInfo);
    }

    @Override
    public void saveApplicationOrder(OiqOrderReqDTO oiqOrderReqDTO) throws Exception{
        OiqOrderReq oiqOrderReq=oiqOrderReqDTO.getOiqOrderReq();
        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        //1询价单 2订单
        OrderBaseInfo orderBaseInfo1=null;
        OrderBaseInfo orderBaseInfo2=null;
        BaseOrderDTO oldBaseOrder=oiqOrderReqDTO.getOrderDTO();

        if(oiqOrderReqDTO.getPortal()){
            //业务线相关数据
            if(ValidationUtil.isEmpty(oiqOrderReqDTO.getBusinessLineDTO())){
                throw new BusinessException(ResultEnumCode.ENUM_ERROR);
            }
            oiqOrderReqDTO.setLineId(oiqOrderReqDTO.getBusinessLineDTO().getConfigId());


            OrderReportDO orderReportDO=new OrderReportDO();
            oiqOrderReqDTO.setOrderReport(orderReportDO.getReportByReq(oiqOrderReq.getReport(),oiqOrderReqDTO.getOrderNo(),oiqOrderReqDTO));

            orderBaseInfo2=oiqOrderReqDTO.getAdd()?orderBaseInfoDO.addOrderByApplication(oiqOrderReqDTO, OrderTypeEnum.OIQ_PORTAL_ORDER):
                    orderBaseInfoDO.updateOrderByApplication(oiqOrderReqDTO,OrderTypeEnum.OIQ_PORTAL_ORDER);
            orderBaseInfoDO.companyInfo(oiqOrderReq,orderBaseInfo2);
            setByBu(oiqOrderReqDTO, orderBaseInfo2);
            //虚拟询价单和订单(订单使用groupNo)
           //如果是提交的情况下 查历史有没有单子 没有的话新增 不做修改
            if(oiqOrderReqDTO.getOiqOrderReq().getType()!=0){
                BaseOrderDTO baseOrderDTO=orderBaseInfoRepository.selectBaseByOrderNo(oiqOrderReqDTO.getInquiryOrderNo());
                if(ValidationUtil.isEmpty(baseOrderDTO)){
                    orderBaseInfo1=orderBaseInfoDO.addOrderByApplication(oiqOrderReqDTO, OrderTypeEnum.OIQ_PORTAL_INQUIRY);
                    orderBaseInfo1.setCompanyName(orderBaseInfo2.getCompanyName());
                    oiqOrderReqDTO.setAddInquiry(1);
                    orderBaseInfo2.setRelateOrderNo(orderBaseInfo1.getOrderNo());
                }

            }

            orderBaseInfo2.setSubState(oiqOrderReq.getType()==0? OrderSubStateEnum.PORTAL_FLG.getIndex():OrderSubStateEnum.NO_OPERATOR.getIndex());
            orderBaseInfoDO.oiqOrderToDml(oiqOrderReqDTO,orderBaseInfo2);

            SysPersonDTO personDTO=oiqOrderReqDTO.getBusinessPerson();
            orderBaseInfo2.setOperatorCode("");
            if(!ValidationUtil.isEmpty(personDTO) && StringUtils.isNotBlank(personDTO.getPersonCode())){
                if(oiqOrderReqDTO.getAddInquiry()==1){
                    orderBaseInfoDO.entityAddCsPerson(orderBaseInfo1,personDTO);
                }
                String operatorCode=ssoTemplateService.getOrderCsCodeAttr(personDTO.getPersonCode());
                if(StringUtils.isNotBlank(operatorCode)){
                    orderBaseInfo2.setOperatorCode(operatorCode);
                }

                if(oiqOrderReqDTO.getOiqOrderReq().getType()!=2){
                    orderBaseInfoDO.entityAddCsPerson(orderBaseInfo2,personDTO);
                }
            }
            oiqOrderReqDTO.getDmlMainReqDTO().setOrderNo(orderBaseInfo2.getOrderNo());
        }else{
            orderBaseInfo2 = new OrderBaseInfo();
            orderBaseInfo2.setOrderId(oldBaseOrder.getOrderId());
            orderBaseInfo2.setStateDate(oiqOrderReqDTO.getDateStr());
            if(oiqOrderReqDTO.getOiqOrderReq().getType()!=0) {
                orderBaseInfo2.setState(BaseOrderStateEnum.WAITSEND.getIndex());
                if (oldBaseOrder.getSubState()!=null && oldBaseOrder.getSubState() == OrderSubStateEnum.BACK.getIndex()) {
                    orderBaseInfo2.setSubState(OrderSubStateEnum.BACK_CONFIRM.getIndex());
                }
                orderBaseInfo2.setConfirmUseState("11");
            }
        }
        //修改的时候 如果历史已经提交过样品寄送的话 状态跳过样品寄送
        if (!oiqOrderReqDTO.getAdd() && oiqOrderReqDTO.getOiqOrderReq().getType()!=0 ){
            Integer hisState=oiqOrderReqDTO.getPortal()?oldBaseOrder.getState():oldBaseOrder.getHisState();
            if(hisState!=null && BaseOrderStateEnum.WAITEXAMINE.getIndex() == hisState){
                orderBaseInfo2.setState(BaseOrderStateEnum.WAITEXAMINE.getIndex());
            }
        }

        if(oiqOrderReqDTO.getAdd()){
            orderBaseInfo2.setIsPayReceived(0);
            orderBaseInfo2.setPayState(0);
            insertEntity(orderBaseInfo2);
        }else{
            updateEntity(orderBaseInfo2);
        }

        if(!ValidationUtil.isEmpty(orderBaseInfo1)){
            orderBaseInfo1.setTmpGroupNo(orderBaseInfo1.getGroupNo());
            orderBaseInfo1.setGroupNo("");
            orderBaseInfo1.setConfirmOrderDate(UseDateUtil.getDateString(new Date()));
            SysPersonDTO sysPersonDTO=oiqOrderReqDTO.getBusinessPerson();
            orderBaseInfo1.setCsCode(sysPersonDTO.getPersonCode());
            orderBaseInfo1.setCsName(sysPersonDTO.getPersonName());
            orderBaseInfo1.setCsPhone(sysPersonDTO.getPersonPhone());
            orderBaseInfo1.setTestLabel(1);
            insertEntity(orderBaseInfo1);
            BaseOrderDTO baseOrderDTO=new BaseOrderDTO();
            baseCopyObj.copyWithNull(baseOrderDTO,orderBaseInfo1);
            baseOrderDTO.setOrderType(String.valueOf(orderBaseInfo1.getOrderType()));
            List<VOOrderAttribute> list=ssoTemplateService.personAttr(baseOrderDTO,orderBaseInfo1.getTmpGroupNo());
            if(!ValidationUtil.isEmpty(list)){
                List<VOOrderAttribute> insertList=list.stream().filter(a->!(AttributeUtil.LAB_NAME.equals(a.getAttrValue())||AttributeUtil.REPORT_LUA.equals(a.getAttrValue())||
                        AttributeUtil.REPORT_FORM.equals(a.getAttrValue()))).collect(Collectors.toList());
                //加实验室
                VOOrderAttribute labAttr = orderAttributeService.getVOOrderAttribute(orderBaseInfo1.getOrderNo(),orderBaseInfo1.getTmpGroupNo(),
                        AttributeUtil.LAB_NAME,orderBaseInfo2.getLabName(),String.valueOf(orderBaseInfo2.getLabId()),1,UseDateUtil.getDateString(new Date()));
                VOOrderAttribute luaAttr = orderAttributeService.getVOOrderAttribute(orderBaseInfo1.getOrderNo(),orderBaseInfo1.getTmpGroupNo(),
                        AttributeUtil.REPORT_LUA,orderBaseInfo2.getReportLua(),orderBaseInfo2.getReportLuaCode(),1,UseDateUtil.getDateString(new Date()));
                VOOrderAttribute formAttr = orderAttributeService.getVOOrderAttribute(orderBaseInfo1.getOrderNo(),orderBaseInfo1.getTmpGroupNo(),
                        AttributeUtil.REPORT_FORM,orderBaseInfo2.getReportForm(),orderBaseInfo2.getReportFormCode(),1,UseDateUtil.getDateString(new Date()));

                insertList.add(labAttr);
                insertList.add(luaAttr);
                insertList.add(formAttr);
                orderAttributeService.insertForeach(insertList);
            }
            orderOperatorLogDomainService.addLog(baseOrderDTO,OrderOperatorTypeEnum.DEMAND,oiqOrderReqDTO.getUserDTO().getLogUserShow());
            Long userId=oiqOrderReqDTO.getUserDTO().getUserId();
            if(userLabelSV.qryUserLabelFlg(userId, UserLabelCodeEnum.INQUIRY_REPURCHASE)==0 &&
                    customLimitService.selectRepurchaseCount(userId)>0) {
                userLabelSV.saveUserLabel(userId,UserLabelCodeEnum.INQUIRY_REPURCHASE);
            }
            //申请表订单的虚拟询价单的业务线
            String code= oiqOrderReqDTO.getBusinessLineDTO().getConfigCode();
            if(StringUtils.isNotBlank(code)){
                userLabelSV.saveUserLabel(userId, ListUtil.add(code));
            }
            oiqOrderReqDTO.getOiqOrderReq().setSaveOrderFlg(1);
            oiqOrderReqDTO.getOiqOrderReq().getApiOtherDTO().setInquiryOrderNo(orderBaseInfo1.getOrderNo());
        }
    }

    private void setByBu(OiqOrderReqDTO oiqOrderReqDTO, OrderBaseInfo orderBaseInfo) {
        if("2700".equals(oiqOrderReqDTO.getOiqOrderReq().getOrderBase().getBu())
            && oiqOrderReqDTO.getOiqOrderReq().getType() != 0){
            BaseOrderDTO baseOrderDTO = orderBaseInfoRepository.selectBaseByOrderNo(
                oiqOrderReqDTO.getOrderNo());
            if (Objects.isNull(baseOrderDTO) || ValidationUtil.isEmpty(baseOrderDTO.getPlatformOrder())) {
                orderBaseInfo.setPlatform("TIC");
                List<OrderApplicationAttrReq> destinationList = oiqOrderReqDTO.getOiqOrderReq()
                    .getDestinationList();
                if (!ValidationUtil.isEmpty(destinationList)) {
                    destinationList
                        .stream()
                        .filter(item -> OrderApplicationAttrDO.DESTINATION_COUNTRY.equals(item.getAttrCode())
                            && !ValidationUtil.isEmpty(item.getAttrValue()))
                        .map(OrderApplicationAttrReq::getAttrValue)
                        .findFirst()
                        .ifPresent(destinationCountry -> {
                            String tfsPlatFormOrder = orderUtilService.getTfsPlatFormOrder(
                                destinationCountry);
                            orderBaseInfo.setPlatformOrder(tfsPlatFormOrder);
                        });
                }
            }
        }
    }

    //这里放domain中，提供给qryInfo和qryForm的初始校验
    public BaseOrderDTO checkOrder(OiqQryInfoReq oiqQryInfoReq, UserDTO userDTO) throws Exception {
        if(StringUtils.isNotBlank(oiqQryInfoReq.getOrderNo())){
            BaseOrderDTO baseOrderDTO = orderBaseInfoCustomService.selectBaseByOrderNo(oiqQryInfoReq.getOrderNo());
            String bu=baseOrderDTO.getBu();
            oiqQryInfoReq.setOldOrder(baseOrderDTO);
            if (ValidationUtil.isEmpty(baseOrderDTO.getUserId()))
                return baseOrderDTO;
            if (ValidationUtil.isEmpty(userDTO)) {
                if (OrderTypeEnum.OIQORDER.getIndex().equals(baseOrderDTO.getOrderType())) {
                    throw new SysException("9978");
                }
                throw new SysException("9978", String.valueOf(baseOrderDTO.getState()));
            }
            if(!CheckUtil.isSame(baseOrderDTO.getUserId(),userDTO.getUserId())) {
                throw new BusinessException(ResultEnumCode.USER_UNABLE);
            }
            //查看有没有申请表数据 当有的时候返回
            OrderApplicationFormDTO orderApplicationFormDTO=orderApplicationFormDomainService.qryApplicationFormByOrder(oiqQryInfoReq.getOrderNo());
            if(!ValidationUtil.isEmpty(orderApplicationFormDTO)){
                return baseOrderDTO;
            }
            //有订单号 没申请表数据 只能是询报价的数据 当这种情况下 查询订单
            if(OrderTypeEnum.OIQORDER.getIndex().equals(baseOrderDTO.getOrderType())){
                //询价单的上一单逻辑
                String orderNoStr=orderApplicationFormDomainService.selectLastFormOrderNoByUserLaseVO(userDTO.getUserId(),bu,null,OrderTypeEnum.OIQORDER);
                oiqQryInfoReq.setUseOld(true);
                if(StringUtils.isBlank(orderNoStr)){
                    return baseOrderDTO;
                }
                return selectBaseByOrderNo(orderNoStr);
            }
        }
        if(!ValidationUtil.isEmpty(userDTO)) {
            oiqQryInfoReq.setUseOld(true);
            String orderNoStr=orderApplicationFormDomainService.selectLastFormOrderNoByUserLaseVO(userDTO.getUserId(),null,oiqQryInfoReq.getPlatformCode(),OrderTypeEnum.OIQ_PORTAL_ORDER);
            if(StringUtils.isBlank(orderNoStr)){
                oiqQryInfoReq.setOldOrder(null);
                return null;
            }
            BaseOrderDTO baseOrderDTO=selectBaseByOrderNo(orderNoStr);
            oiqQryInfoReq.setOldOrder(baseOrderDTO);
            return baseOrderDTO;
        }
        return null;
    }

    @Override
    public void getFormInfoMore(OiqOrderDTO oiqOrderDTO, BaseOrderDTO baseOrderDTO) throws Exception {
        String orderNo=baseOrderDTO.getOrderNo();
        //询报价的base是自己 申请表订单是上一单的
        baseCopyObj.copyWithNull(oiqOrderDTO.getOrderBase(),oiqOrderDTO.getPortal()?baseOrderDTO:oiqOrderDTO.getCurrentOrder());
        if(!oiqOrderDTO.getPortal()){
            oiqOrderDTO.getOrderBase().setBusinessPersonEmail(oiqOrderDTO.getOrderBase().getCsEmail());
        }
        if(oiqOrderDTO.getOrderBase().getApplicationLineId()!=null){
            BusinessLineDTO businessLineDTO=centerRespository.qryBusinessLineDTOByLineId(oiqOrderDTO.getOrderBase().getApplicationLineId());
            if(!ValidationUtil.isEmpty(businessLineDTO)){
                oiqOrderDTO.getOrderBase().setBusinessName(businessLineDTO.getConfigName());
                oiqOrderDTO.getOrderBase().setBusinessCode(businessLineDTO.getPlatformCode());
            }
        }

        orderApplicationFormDomainService.getFormByOrderNo(orderNo,oiqOrderDTO);
        orderReportDomainService.getReportByOrderNo(baseOrderDTO,oiqOrderDTO);

        //老的询报价逻辑 历史的
        if(oiqOrderDTO.getUseUseOrder()==1 && !oiqOrderDTO.getPortal() &&oiqOrderDTO.getApplicationLinkNullFlg() ){
            Map map = new HashMap();
            map.put(SelectBaseUtil.USER_ID,baseOrderDTO.getUserId());
            List<UserCompanyDTO> list = userCompanyService.selectListByMap(map);
            if (list.size() > 0) {
                oiqOrderDTO.getApplication().setCompanyNameCn(list.get(0).getCompanyName());
                oiqOrderDTO.getApplication().setCompanyNameEn(list.get(0).getCompanyNameEn());
                oiqOrderDTO.getApplication().setCompanyAddressEn(list.get(0).getCompanyAddrEn());
                oiqOrderDTO.getReport().setReportCompanyNameCn(list.get(0).getCompanyName());
                oiqOrderDTO.getReport().setReportCompanyNameEn(list.get(0).getCompanyNameEn());
                oiqOrderDTO.getReport().setReportAddressEn(list.get(0).getCompanyAddrEn());
            }
            List<UserInfoDTO> userInfoDTOS = userInfoService.selectListByMap(map);
            if (userInfoDTOS.size() > 0) {
                oiqOrderDTO.getApplication().setLinkPhone(userInfoDTOS.get(0).getUserPhone());
                oiqOrderDTO.getApplication().setLinkPerson(userInfoDTOS.get(0).getUserNick());
                oiqOrderDTO.getApplication().setLinkEmail(userInfoDTOS.get(0).getUserEmail());
            }
        }

        //申请表添加发票
        OrderInvoiceDTO orderInvoiceDTO = orderInvoiceDomainService.qryOneOrderInvoiceByOrder(orderNo);
        if (orderInvoiceDTO != null) {
            OiqOrderInvoiceDTO oiqOrderInvoiceDTO=new OiqOrderInvoiceDTO();
            baseCopyObj.copyWithNull(oiqOrderInvoiceDTO,orderInvoiceDTO);
            UserInvoiceDTO userInvoiceDTO=new UserInvoiceDTO();
            baseCopyObj.copyWithNull(userInvoiceDTO,orderInvoiceDTO);
            oiqOrderInvoiceDTO.setInvoice(userInvoiceDTO);
            oiqOrderInvoiceDTO.setInvoiceType(orderInvoiceDTO.getInvoiceType());
            oiqOrderInvoiceDTO.setInvoiceId(orderInvoiceDTO.getInvoiceId());
            //发票的地址处理 历史给他展示
            if(orderInvoiceDTO.getAddressId()!=null){
                OiqUserAddressDTO userAddressDTO=new OiqUserAddressDTO();
                baseCopyObj.copyWithNull(userAddressDTO,orderInvoiceDTO);
                List<OiqAddressDTO> list=new ArrayList<>();
                list.add(new OiqAddressDTO(orderInvoiceDTO.getAddressId(),userAddressDTO));
                oiqOrderInvoiceDTO.setAddressList(list);
            }
            oiqOrderDTO.setOrderInvoice(oiqOrderInvoiceDTO);
        }
        orderExpressDomainService.oiqAddAddress(orderNo,oiqOrderDTO);
        //orderLink取值逻辑调整
        orderLinkDomainService.addLinkToOrder(orderNo,oiqOrderDTO);

        OrderShippingDTO orderShippingDTO=orderShippingDomainService.qryOrderShippingByOrder(orderNo);
        if(!ValidationUtil.isEmpty(orderShippingDTO)){
            oiqOrderDTO.setOrderShipping(orderShippingDTO);
        }
        List<OrderShippingContactDTO> contactDTOList=orderShippingContactDomainService.qryOrderShippingContactListByOrderNo(orderNo);
        if(!ValidationUtil.isEmpty(contactDTOList)){
            OrderShippingContactDO.listToOiqDTO(oiqOrderDTO,contactDTOList);
        }


        //项目和样品逻辑处理
        List<OrderLabelDTO> orderLabelList= orderLabelDomainService.selectList(Arrays.asList(orderNo), OrderLabelCodeEnum.TAILS);
        Map<String, String> orderLabelFlg=orderLabelList.stream().collect(Collectors.toMap(E->E.getOrderNo(), E->E.getLabelValue(), (key1, key2) -> key2));
        oiqOrderDTO.setTails(orderLabelFlg.getOrDefault(orderNo,"0"));
        BaseOrderDTO old=oiqOrderDTO.getCurrentOrder();
        if(!ValidationUtil.isEmpty(old)){
            //先样品在项目 样品的值有用，样品配置和样品分类逻辑使用applicationLineId
            List<OiqSampleDTO> sampleDTOList=orderSampleDomainService.getOiqOrderSample(old.getOrderNo(),old.getGroupNo(),old.getApplicationLineId());
            oiqOrderDTO.setSampleList(sampleDTOList);
            oiqOrderDTO.setSampleCategory(getSampleCategoryBySampleList(sampleDTOList, oiqOrderDTO.getOrderBase().getBusinessCode(), old.getBu(), old.getApplicationLineId()));
            //增加项目
            orderDetailDomainService.getItemByOrderNo(old.getOrderNo(),old.getGroupNo(),oiqOrderDTO);
        }
        //获取订单分享的数据
        List<CustomerIdDTO> customerIdDTOList=orderCustomerDomainService.qryCustomerByOrderNo(orderNo);
        oiqOrderDTO.setCustomerList(customerIdDTOList);
    }
    @Override
    public void getShareFormInfoMore(OiqOrderDTO oiqOrderDTO, BaseOrderDTO baseOrderDTO) throws Exception {
        String orderNo=baseOrderDTO.getOrderNo();
        //询报价的base是自己 申请表订单是上一单的
        baseCopyObj.copyWithNull(oiqOrderDTO.getOrderBase(),baseOrderDTO);
        if(!oiqOrderDTO.getPortal()){
            oiqOrderDTO.getOrderBase().setBusinessPersonEmail(oiqOrderDTO.getOrderBase().getCsEmail());
        }
        if(oiqOrderDTO.getOrderBase().getApplicationLineId()!=null){
            BusinessLineDTO businessLineDTO=centerRespository.qryBusinessLineDTOByLineId(oiqOrderDTO.getOrderBase().getApplicationLineId());
            if(!ValidationUtil.isEmpty(businessLineDTO)){
                oiqOrderDTO.getOrderBase().setBusinessName(businessLineDTO.getConfigName());
                oiqOrderDTO.getOrderBase().setBusinessCode(businessLineDTO.getPlatformCode());
            }
        }

        orderApplicationFormDomainService.getFormByOrderNo(orderNo,oiqOrderDTO);
        orderReportDomainService.getReportByOrderNo(baseOrderDTO,oiqOrderDTO);


        OrderShippingDTO orderShippingDTO=orderShippingDomainService.qryOrderShippingByOrder(orderNo);
        if(!ValidationUtil.isEmpty(orderShippingDTO)){
            oiqOrderDTO.setOrderShipping(orderShippingDTO);
        }
        List<OrderShippingContactDTO> contactDTOList=orderShippingContactDomainService.qryOrderShippingContactListByOrderNo(orderNo);
        if(!ValidationUtil.isEmpty(contactDTOList)){
            OrderShippingContactDO.listToOiqDTO(oiqOrderDTO,contactDTOList);
        }

        //申请表添加发票
        OrderInvoiceDTO orderInvoiceDTO = orderInvoiceDomainService.qryOneOrderInvoiceByOrder(orderNo);
        if (orderInvoiceDTO != null) {
            OiqOrderInvoiceDTO oiqOrderInvoiceDTO=new OiqOrderInvoiceDTO();
            baseCopyObj.copyWithNull(oiqOrderInvoiceDTO,orderInvoiceDTO);
            UserInvoiceDTO userInvoiceDTO=new UserInvoiceDTO();
            baseCopyObj.copyWithNull(userInvoiceDTO,orderInvoiceDTO);
            oiqOrderInvoiceDTO.setInvoice(userInvoiceDTO);
            oiqOrderInvoiceDTO.setInvoiceType(orderInvoiceDTO.getInvoiceType());
            oiqOrderInvoiceDTO.setInvoiceId(orderInvoiceDTO.getInvoiceId());
            //发票的地址处理 历史给他展示
            if(orderInvoiceDTO.getAddressId()!=null){
                OiqUserAddressDTO userAddressDTO=new OiqUserAddressDTO();
                baseCopyObj.copyWithNull(userAddressDTO,orderInvoiceDTO);
                List<OiqAddressDTO> list=new ArrayList<>();
                list.add(new OiqAddressDTO(orderInvoiceDTO.getAddressId(),userAddressDTO));
                oiqOrderInvoiceDTO.setAddressList(list);
            }
            oiqOrderDTO.setOrderInvoice(oiqOrderInvoiceDTO);
        }
        orderExpressDomainService.oiqAddAddress(orderNo,oiqOrderDTO);
        //orderLink取值逻辑调整
        orderLinkDomainService.addLinkToOrder(orderNo,oiqOrderDTO);
        //项目和样品逻辑处理
        //先样品在项目 样品的值有用，样品配置和样品分类逻辑使用applicationLineId
        List<OiqSampleDTO> oiqSampleDTOList=orderSampleDomainService.getOiqOrderSample(baseOrderDTO.getOrderNo(),baseOrderDTO.getGroupNo(),oiqOrderDTO.getCurrentOrder().getApplicationLineId());
        oiqOrderDTO.setSampleList(oiqSampleDTOList);
        oiqOrderDTO.setSampleCategory(getSampleCategoryBySampleList(oiqSampleDTOList, oiqOrderDTO.getOrderBase().getBusinessCode(), oiqOrderDTO.getCurrentOrder().getBu(), oiqOrderDTO.getCurrentOrder().getApplicationLineId()));
        //增加项目
        orderDetailDomainService.getItemByOrderNo(baseOrderDTO.getOrderNo(),baseOrderDTO.getGroupNo(),oiqOrderDTO);

        //获取订单分享的数据
        List<CustomerIdDTO> customerIdDTOList=orderCustomerDomainService.qryCustomerByOrderNo(orderNo);
        oiqOrderDTO.setCustomerList(customerIdDTOList);
    }

    @Override
    public OrderBaseInfoMoreDTO selectMoreDTOByOrderNo(String orderNo,Long userId) {
        OrderBaseInfoMoreDTO orderBaseInfoMoreDTO=orderBaseInfoMapper.selectDTOByOrderNo(orderNo);
        if (orderBaseInfoMoreDTO == null) {
            throw new BusinessException(ResultCode.ORDER_IS_NULL);
        }
        if (!CheckUtil.isSame(orderBaseInfoMoreDTO.getUserId(),userId)) {
            throw new BusinessException(ResultEnumCode.USER_IS_NULL);
        }
        return orderBaseInfoMoreDTO;
    }

    public OrderBaseInfoMoreDTO selectMoreDTOByOrderNo(String orderNo) {
        OrderBaseInfoMoreDTO orderBaseInfoMoreDTO=orderBaseInfoMapper.selectDTOByOrderNo(orderNo);
        if (orderBaseInfoMoreDTO == null) {
            throw new BusinessException(ResultCode.ORDER_IS_NULL);
        }
        return orderBaseInfoMoreDTO;
    }


    public void updateBaseIsRead(Long orderId, Boolean flg) {
        if(!flg){
            return;
        }
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        orderBaseInfo.setOrderId(orderId);
        orderBaseInfo.setIsRead(1);
        updateEntity(orderBaseInfo);
    }
    @Override
    public BaseOrderDTO selectBaseByOrderNo(String orderNo) throws Exception{
        BaseOrderDTO baseOrderDTO=orderBaseInfoMapper.selectBaseByOrderNo(orderNo);
        if(baseOrderDTO==null){
            throw new BusinessException(ResultCode.ORDER_IS_NULL);
        }
        return baseOrderDTO;
    }
    @Override
    public BaseOrderDTO selectBaseByOrderNo(String orderNo,Long userId) throws Exception{
        BaseOrderDTO baseOrderDTO=selectBaseByOrderNo(orderNo);
        if (!CheckUtil.isSame(baseOrderDTO.getUserId(),userId)) {
            throw new BusinessException(ResultCode.ORDER_IS_NULL);
        }
        return baseOrderDTO;
    }

    @Override
    public JSONObject checkMsdsOrderNo(String orderNo) throws Exception {
        BaseOrderDTO baseOrderDTO=orderBaseInfoMapper.selectBaseByOrderNo(orderNo);

        if(baseOrderDTO==null)
            throw new BusinessException(ResultEnumCode.ORDER_NULL);


        List<OrderApplicationAttrDTO> orderApplicationAttrDTOList = new ArrayList<>();
        Map<String,Object> map = new HashMap<>();
        map.put(SelectBaseUtil.ORDER_NO,orderNo);
        map.put(SelectBaseUtil.STATE,1);
        map.put(SelectMapUtil.CLASSIFY_TYPE, OrderApplicationClassifyTypeEnum.MSDS.getIndex());
        orderApplicationAttrDTOList = orderApplicationAttrDomainService.selectListByMap(map);
        if(ValidationUtil.isEmpty(orderApplicationAttrDTOList) )
            throw  new BusinessException(ResultEnumCode.IS_NOT_SDS_MATERALS);


        return jsonTransUtil.fromBoToJson(BaseOrderDTO.class,baseOrderDTO, BaseOrderFilter.OrderApplicationOther.class);
    }

    @Override
    public Map<String,String> getLastOrderNoByInquiry(List<String> relateOrderNoList){
        List<ToOrderDTO> list=orderBaseInfoMapper.getLastOrderNoByInquiry(relateOrderNoList);
        return list.stream().collect(Collectors.toMap(ToOrderDTO::getRelateOrderNo, ToOrderDTO::getOrderNo, (key1, key2) -> key2));
    }

    @Override
    public List<OrderBaseInfoDTO> selectNewListByReq(OrderBaseInfoQryReq orderBaseInfoQryReq) {
        return orderBaseInfoMapper.selectNewListByReq(orderBaseInfoQryReq);
    }

    @Override
    public int selectNewListCountByReq(OrderBaseInfoQryReq orderBaseInfoQryReq) {
        return orderBaseInfoMapper.selectNewListCountByReq(orderBaseInfoQryReq);
    }

    @Override
    public List<StateAndNumDTO> selectStateGroup(Map<String, Object> map) {
        return orderBaseInfoCustomMapper.selectStateGroup(map);
    }

    @Override
    public int selectEndNumStateByRead(Map<String, Object> map) {
        return orderBaseInfoCustomMapper.selectEndNumStateByRead(map);
    }

    @Override
    public void updateStateByLock(BaseOrderDTO baseOrderDTO,int state,String closeCode) {
        OrderLockDTO orderLockDTOUpdate = new OrderLockDTO();
        orderLockDTOUpdate.setOrderId(baseOrderDTO.getOrderId());
        orderLockDTOUpdate.setOldState(baseOrderDTO.getState());
        orderLockDTOUpdate.setCloseCode(closeCode);
        String closeReason = orderUtilService.getEnumStringByKey(RedisKeyUtil.USER_CLOSE,closeCode);
        orderLockDTOUpdate.setCloseReason(closeReason);
        orderLockDTOUpdate.setHisState(baseOrderDTO.getState());
        orderLockDTOUpdate.setNewState(state);
        orderLockDTOUpdate.setStateDate(UseDateUtil.getDateString(new Date()));
        int n =orderBaseInfoCustomMapper.updateStateByLock(orderLockDTOUpdate);
        if (n == 0) {
            throw new BusinessException(ResultEnumCode.ORDER_STATE_ERROR);
        }
    }

    @Override
    public void updateStateByRead(Long userId) {
         orderBaseInfoCustomMapper.updateStateByRead(userId);
    }

    @Override
    public RSTSSaveOrderBaseDTO addOrUpdateRstsOrder(RstsOrderSaveReq rstsOrderSaveReq, UserDTO userDTO, String baseKey) throws Exception {
        RSTSSaveOrderBaseDTO rstsSaveOrderBaseDTO = new RSTSSaveOrderBaseDTO();
        String groupNo = customCodeService.getNewGroupNo();
        String orderNo = customCodeService.getRSTSOrderNo();

        String dateStr = UseDateUtil.getDateString(new Date());

        //确认是新增 还是修改(订单号和groupNo)
        Boolean isAdd = true;
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = new OrderBaseInfoCheckDTO(orderNo, userDTO.getUserId());
        if (StringUtils.isNotBlank(rstsOrderSaveReq.getOrderNo())) {
            orderBaseInfoCheckDTO = orderBaseInfoService.checkOrderBase(rstsOrderSaveReq.getOrderNo(), userDTO, OrderTypeEnum.RSTS.getIndex());
            orderNo = rstsOrderSaveReq.getOrderNo();
            groupNo = orderBaseInfoCheckDTO.getGroupNo();
            isAdd = false;
        }



        //获取step2的sampleInfo和item
        SampleInfoDTO sampleInfoDTO = irstsRedisService.getSampleInfoDTO(baseKey, userDTO, true,rstsOrderSaveReq.getCustId());
        if (sampleInfoDTO.getThrowType()) {
            throw new BusinessException(ResultEnumCode.RSTS_ITEM_ISNULL);
        }
        if (sampleInfoDTO.getThrowRoType()) {
            throw new BusinessException(ResultEnumCode.RSTS_ITEM_ROHS_NULL);
        }
        List<SampleItemDTO> sampleItemDTOList = sampleInfoDTO.getList();

        //获取step3的rstsApplicationReq
        RstsApplicationReq rstsApplicationReq = irstsRedisService.getRstsApplication(baseKey);
        if (rstsApplicationReq == null) {
            throw new BusinessException(ResultEnumCode.REDIS_ERROR);
        }
        //校验企业id 是否存在 虚拟企业直接过滤掉
        CustInvoiceDTO custInvoiceDTO=custInvoiceDomainService.checkCustInvoiceByCustId(rstsOrderSaveReq.getCustId(),rstsApplicationReq.getApplicationReq().getInvoiceId());



        irstsOrderUtilService.addSampleInfoDTOByLab(new RstsOrderDetailDTO(rstsApplicationReq.getLabCode()), sampleInfoDTO, userDTO,rstsOrderSaveReq.getCustId());
        if (sampleInfoDTO.getThrowLabType()) {
            rstsApplicationReq.setLabCode("");
            redisClient.setValue(baseKey, RedisKeyUtil.RSTS_APPLICATION, JSON.toJSONString(rstsApplicationReq));
            throw new BusinessException(ResultEnumCode.RSTS_ITEM_LAB_NULL);
        }
        int sampleSize = sampleItemDTOList.size();//总的样品数据

        //新的计算费用的逻辑调整
        irstsOrderUtilService.getOrderPriceNew(rstsApplicationReq,sampleInfoDTO, sampleSize,sampleItemDTOList);
        //当物流不为空的时候 直接变状态
        Boolean expressFlg= StringUtils.isNotBlank(rstsOrderSaveReq.getExpressNo())?true:false;
        //
        //修改的情况下 入参为空
        if(!isAdd && !expressFlg){
            OrderApplicationAttrDTO expressCodeAttr=orderApplicationAttrService.selectCustom(orderNo, AttributeUtil.EXPRESS_CODE);
            OrderApplicationAttrDTO expressNoAttr=orderApplicationAttrService.selectCustom(orderNo, AttributeUtil.EXPRESS_NO);
            if(!ValidationUtil.isEmpty(expressNoAttr)){
                rstsOrderSaveReq.setExpressCode(expressCodeAttr.getAttrValue());
                rstsOrderSaveReq.setExpressNo(expressNoAttr.getAttrValue());
                expressFlg=true;
            }
        }


        int state = rstsOrderSaveReq.getType() == 0 ? BaseOrderStateEnum.WAITAPPLY.getIndex() :
                (expressFlg? BaseOrderStateEnum.WAITEXAMINE.getIndex(): BaseOrderStateEnum.WAITSEND.getIndex());
        //获取销售的联系人和电话
        CustBossDTO custBossDTO = custInfoSV.qryCustBossDTO(userDTO.getUserId(),rstsOrderSaveReq.getCustId());
        custBossDTO.setBossNo(rstsOrderSaveReq.getBossNo());
        custBossDTO.setCustId(rstsOrderSaveReq.getCustId());

        OtherCheckHabitReq otherCheckHabitReq = rstsApplicationReq.getOtherCheckHabitReq();
        Map<String,String> attrMap=new HashMap<>();
        attrMap.put(AttributeUtil.LAB_AREA_CODE,rstsApplicationReq.getLabCode());
        attrMap.put(AttributeUtil.CRM_BASE_LAB_CODE,rstsOrderSaveReq.getBelongLabCode());
        attrMap.put(AttributeUtil.VBA_ACCOUNT,rstsOrderSaveReq.getVbaAccount());
        attrMap.put(AttributeUtil.EXPRESS_CODE,rstsOrderSaveReq.getExpressCode());
        attrMap.put(AttributeUtil.EXPRESS_NO,rstsOrderSaveReq.getExpressNo());
        attrMap.put(AttributeUtil.INVOICE_RECEIVE_EMAIL,rstsApplicationReq.getApplicationReq().getInvoiceReceiveEmail());
        attrMap.put(AttributeUtil.RSTS_IS_SAME_TO_APPLICATION,!ValidationUtil.isEmpty(otherCheckHabitReq)?otherCheckHabitReq.getRstsIsSameToApplication():"0");
        attrMap.put(AttributeUtil.RSTS_IS_CHECK_REMARK,!ValidationUtil.isEmpty(otherCheckHabitReq)?otherCheckHabitReq.getRstsIsCheckRemark():"0");
        //新增才有sampleCode
        if(isAdd){
            attrMap.put(AttributeUtil.SAMPLE_CODE, rstsOrderSaveReq.getSampleCode());
            attrMap.put(AttributeUtil.SAMPLE_ID, rstsOrderSaveReq.getSampleId());
        }
        orderApplicationAttrService.insertAttrMap(attrMap, orderNo, dateStr, AttributeUtil.RO_ATTRIBUTE);




        EnumDTO enumDTO = orderUtilService.getEnumKey(RedisKeyUtil.RSTS_LAB_CODE, rstsApplicationReq.getLabCode());
        Long orderId = 0L;
        String currency = sampleInfoDTO.getCurrency();//币种
        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        if (isAdd) {
            VOOrderBaseInfo voOrderBaseInfoAdd = orderBaseInfoService.addBaseVOOrder(userDTO, dateStr);
            orderBaseInfoService.addVOByApplication(voOrderBaseInfoAdd, rstsApplicationReq, state);
            voOrderBaseInfoAdd.setOrderNo(orderNo);
            voOrderBaseInfoAdd.setOrderType(Integer.parseInt(OrderTypeEnum.RSTS.getIndex()));
            voOrderBaseInfoAdd.setGroupNo(groupNo);
            voOrderBaseInfoAdd.setCurrency(currency);
            voOrderBaseInfoAdd.setBu("EET");
            voOrderBaseInfoAdd.setLabId(Long.parseLong(enumDTO.getEnumExtend()));
            voOrderBaseInfoAdd.setLabName(enumDTO.getEnumName());
            voOrderBaseInfoAdd.setIsTest(userDTO.getIsTest());
            voOrderBaseInfoAdd.setFromSource(redisClient.getValue(baseKey,RedisKeyUtil.FROM_SOURCE));
            voOrderBaseInfoAdd.setFromUrl(redisClient.getValue(baseKey,RedisKeyUtil.FROM_URL));
            if(rstsOrderSaveReq.getCrmFlg()==1 ){
                voOrderBaseInfoAdd.setCsCode(userDTO.getCsCode());
                voOrderBaseInfoAdd.setCsEmail(userDTO.getCsEmail());
                voOrderBaseInfoAdd.setFromSource(FromSourceEnum.getCrmShow(voOrderBaseInfoAdd.getFromSource()));
            }
            orderBaseInfoDO.addBaseByBoss(voOrderBaseInfoAdd, custBossDTO);
            if (!ValidationUtil.isEmpty(rstsApplicationReq.getApplicationReq().getInvoiceAddress())) {
                voOrderBaseInfoAdd.setProvince(rstsApplicationReq.getApplicationReq().getInvoiceAddress().getProvince());
                voOrderBaseInfoAdd.setCity(rstsApplicationReq.getApplicationReq().getInvoiceAddress().getCity());
            }
            if(rstsOrderSaveReq.getType() == 1){
                voOrderBaseInfoAdd.setConfirmOrderDate(dateStr);
            }

            orderBaseInfoService.insertSelective(voOrderBaseInfoAdd);


            orderId = voOrderBaseInfoAdd.getOrderId();
        } else {//修改金额
            VOOrderBaseInfo voOrderBaseInfoUpdate = new VOOrderBaseInfo();
            orderId = orderBaseInfoCheckDTO.getOrderId();
            voOrderBaseInfoUpdate.setOrderId(orderBaseInfoCheckDTO.getOrderId());
            orderBaseInfoService.addVOByApplication(voOrderBaseInfoUpdate, rstsApplicationReq, state);
            orderBaseInfoDO.addBaseByBoss(voOrderBaseInfoUpdate, custBossDTO);
            if (!ValidationUtil.isEmpty(rstsApplicationReq.getApplicationReq().getInvoiceAddress())) {
                voOrderBaseInfoUpdate.setProvince(rstsApplicationReq.getApplicationReq().getInvoiceAddress().getProvince());
                voOrderBaseInfoUpdate.setCity(rstsApplicationReq.getApplicationReq().getInvoiceAddress().getCity());
            }
            if(rstsOrderSaveReq.getCrmFlg()==1 ){
                voOrderBaseInfoUpdate.setCsCode(userDTO.getCsCode());
                voOrderBaseInfoUpdate.setCsEmail(userDTO.getCsEmail());
            }

            voOrderBaseInfoUpdate.setIsTest(userDTO.getIsTest() );
            voOrderBaseInfoUpdate.setCurrency(currency);
            voOrderBaseInfoUpdate.setBu("EET");
            voOrderBaseInfoUpdate.setLabId(Long.parseLong(enumDTO.getEnumExtend()));
            voOrderBaseInfoUpdate.setLabName(enumDTO.getEnumName());

            BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(orderBaseInfoCheckDTO.getOrderNo());
            if(rstsOrderSaveReq.getType() == 1){
                voOrderBaseInfoUpdate.setConfirmOrderDate(dateStr);
                if ( baseOrderDTO.getHisState()!=null && BaseOrderStateEnum.WAITEXAMINE.getIndex() == baseOrderDTO.getHisState()) {
                    voOrderBaseInfoUpdate.setState(BaseOrderStateEnum.WAITEXAMINE.getIndex());
                }
            }

            orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfoUpdate);
        }

        if (!isAdd) {
            irstsDetailService.delRSTSDetail(orderNo, groupNo);
        }

        rstsSaveOrderBaseDTO.setAdd(isAdd);
        rstsSaveOrderBaseDTO.setOrderNo(orderNo);
        rstsSaveOrderBaseDTO.setGroupNo(groupNo);
        rstsSaveOrderBaseDTO.setSampleItemDTOList(sampleItemDTOList);
        rstsSaveOrderBaseDTO.setRstsApplicationReq(rstsApplicationReq);
        rstsSaveOrderBaseDTO.setSampleInfoDTO(sampleInfoDTO);
        rstsSaveOrderBaseDTO.setDateStr(dateStr);
        rstsSaveOrderBaseDTO.setCustBossDTO(CustInvoiceDTO.invoiceToBoss(custBossDTO,custInvoiceDTO));
        rstsSaveOrderBaseDTO.setExpressFlg(expressFlg);
        rstsSaveOrderBaseDTO.setOrderId(orderId);
        rstsSaveOrderBaseDTO.setEnumDTO(enumDTO);
        rstsSaveOrderBaseDTO.setCustInvoiceDTO(custInvoiceDTO);
        return rstsSaveOrderBaseDTO;
    }



    public void addCustIdAndUserId(Long custId, Long userId, Map<String, Object> map) {
        Long custIdByCommon = custInfoSV.getCustIdByCommon();
        List<Long> custIdList = new ArrayList<>();
        custIdList.add(custIdByCommon);
        custIdList.add(custId);
        map.put(SelectListUtil.CUST_ID_LIST,custIdList);
        map.put(SelectMapUtil.USER_ID, userId);
        map.put(SelectMapUtil.ORDER_TYPE,OrderTypeEnum.RSTS.getIndex());
    }

    @Override
    public List<OrderBaseInfoDTO> qryAllParentDetailItemByMap(Long custId, Long userId,List<Long> itemIdList) {
        Map<String, Object> map = getQryAllParentDetailItemMap(custId, userId, itemIdList);
        return orderBaseInfoMapper.qryAllParentDetailItemByMap(map);
    }

    private Map<String, Object> getQryAllParentDetailItemMap(Long custId, Long userId, List<Long> itemIdList) {
        Map<String,Object> map = new HashMap<>();
        addCustIdAndUserId(custId, userId,map);
        map.put(SelectMapUtil.PARENT_DETAIL_ID,-1);
        map.put(SelectListUtil.STATE_LIST,Arrays.asList(
                OrderStateEnum.WAITSEND.getIndex(),
                OrderStateEnum.WAITEXAMINE.getIndex(),
                OrderStateEnum.SERVICE.getIndex(),
                OrderStateEnum.END.getIndex()));
        map.put(SelectListUtil.ITEM_ID_LIST, itemIdList);
        map.put(SelectMapUtil.USER_ID, null);
        return map;
    }


    @Override
    public List<RstsPersonCenterDTO> qryOrderNumByState(VOOrderBaseInfo voOrderBaseInfo) {
        return orderBaseInfoMapper.qryOrderNumByState(voOrderBaseInfo);
    }

    @Override
    public List<RstsPersonCenterDTO> qryNotPaySubList(VOOrderBaseInfo voOrderBaseInfo) {
        return orderBaseInfoMapper.qryNotPaySubList(voOrderBaseInfo);
    }




    @Override
    public List<RstsPersonCenterDTO> qryNotPayMainList(VOOrderBaseInfo voOrderBaseInfo) {
        return orderBaseInfoMapper.qryNotPayMainList(voOrderBaseInfo);
    }

    @Override
    public List<OrderBaseInfoDTO> qryList(OrderBaseInfoVO orderBaseInfoVO) {
        return orderBaseInfoMapper.qryList(orderBaseInfoVO);
    }

    @Override
    public List<OrderBaseInfoDTO> qryOrderList(RstsPersonalCenterReq rstsPersonalCenterReq, UserDTO userDTO) {
        Map<String,Object> map = new HashMap<>();
        addCustIdAndUserId(rstsPersonalCenterReq.getCustId(),userDTO.getUserId(),map);
        map.put(SelectBaseUtil.USE_LIMIT,"useLimit");
        map.put(SelectBaseUtil.LIMIT_START,0);
        map.put(SelectBaseUtil.LIMIT_END,rstsPersonalCenterReq.getLimitEnd());
        List<OrderBaseInfoDTO> list = orderBaseInfoMapper.qryListByMap(map);
        if(!ValidationUtil.isEmpty(list)){
            listAddMore(list);
        }
        return list;
    }

    public List<OrderBaseInfoDTO> listAddMore(List<OrderBaseInfoDTO> orderBaseInfoDTOS) {
        List<String> orderNoAll = new ArrayList<>();
        List<Long> custIdList=new ArrayList<>();
        for (OrderBaseInfoDTO orderBaseInfoDTO : orderBaseInfoDTOS) {
            orderNoAll.add(orderBaseInfoDTO.getOrderNo());
            custIdList.add(orderBaseInfoDTO.getCustId());
        }
        Map<String,Object> map = new HashMap<>();
        map.put(SelectListUtil.ORDER_NO_LIST, orderNoAll);
        map.put(SelectMapUtil.PARENT_DETAIL_ID, -1);
        List<OrderDetailDTO> orderDetailList = orderDetailService.selectListByGroupMap(map);
        Map<String, List<OrderDetailDTO>> detailMap = orderDetailList.stream().collect(Collectors.groupingBy(E -> E.getOrderNo()));

        List<OrderSampleDTO> orderSampleDTOList = orderSampleService.selectListByMap(map);
        Map<String, List<OrderSampleDTO>> sampleMap = orderSampleDTOList.stream().collect(Collectors.groupingBy(E -> E.getOrderNo()));

        List<CustInfoDTO> custInfoDTOList=custInfoSV.qryCustByBusi(custIdList);
        Map<Long, String> custInfoDTOMap = custInfoDTOList.stream().collect(Collectors.toMap(E->E.getCustId(),
                E-> org.apache.commons.lang3.StringUtils.isBlank(E.getCustCode())?"":E.getCustCode(), (key1, key2) -> key2));


        Map<String, Object> qryMap = new HashMap<>();
        qryMap.put(SelectListUtil.ORDER_NO_LIST,orderNoAll);
        qryMap.put(SelectMapUtil.ATTR_CODE,AttributeUtil.SAMPLE_CODE );
        List<VOOrderApplicationAttr> attrDTOList = orderApplicationAttrService.selectVOListByMap(qryMap);
        Map<String, String> attrDTOMap = attrDTOList.stream().collect(Collectors.toMap(E->E.getOrderNo(),
                E-> org.apache.commons.lang3.StringUtils.isBlank(E.getAttrValue())?"":E.getAttrValue(), (key1, key2) -> key2));

        Map<String, Object> qryIdMap = new HashMap<>();
        qryIdMap.put(SelectListUtil.ORDER_NO_LIST,orderNoAll);
        qryIdMap.put(SelectMapUtil.ATTR_CODE,AttributeUtil.SAMPLE_ID );
        List<VOOrderApplicationAttr> attrDTOIdList = orderApplicationAttrService.selectVOListByMap(qryIdMap);
        Map<String, String> attrDTOIdMap = attrDTOIdList.stream().collect(Collectors.toMap(E->E.getOrderNo(),
                E-> org.apache.commons.lang3.StringUtils.isBlank(E.getAttrValue())?"":E.getAttrValue(), (key1, key2) -> key2));


        //补差价数据
        List<SubOrderDTO> subOrderDTOList = new ArrayList<>();
        if (!ValidationUtil.isEmpty(orderNoAll)) {
            Map mapAtt = new HashMap();
            mapAtt.put(SelectListUtil.RELATE_ORDER_NO_LIST, orderNoAll);
            subOrderDTOList = subOrderService.selectListByMap(mapAtt);
        }


        Map<String, List<SubOrderDTO>> subOrderGroup = subOrderDTOList.stream().collect(Collectors.groupingBy(E -> E.getRelateOrderNo()));


        List<String> qryOrderNoAll=new ArrayList<>();
        qryOrderNoAll.addAll(orderNoAll);
        qryOrderNoAll.addAll(subOrderDTOList.stream().map(SubOrderDTO::getOrderNo).collect(Collectors.toList()));
        Map<String, Integer> amountMap=orderPayDomainService.qryPayAmount(qryOrderNoAll);

        for (int n = 0; n < orderBaseInfoDTOS.size(); n++) {
            OrderBaseInfoDTO orderBaseInfoDTO = orderBaseInfoDTOS.get(n);
            if(RoDO.checkToPayAmount(orderBaseInfoDTO.getMonthPay(),orderBaseInfoDTO.getPayState(),orderBaseInfoDTO.getCurrency(),orderBaseInfoDTO.getState())) {
                orderBaseInfoDTO.setToPayAmount(RoDO.getToPayAmountByOrder(amountMap,orderBaseInfoDTO.getOrderNo(),
                        orderBaseInfoDTO.getRealAmount(),orderBaseInfoDTO.getPlatformAmount()));
            }

            //最后一条修改记录
            OrderOperatorLogDTO mainLog=orderOperatorLogDomainService.getLastLog(orderBaseInfoDTO.getOrderNo()
                    ,Arrays.asList(OrderOperatorTypeEnum.RO_PRICE.getIndex()),0,"");
            orderBaseInfoDTO.setOrderOperatorLogDTO(mainLog);

            BigDecimal basePrice=orderBaseInfoDTO.getPlatformAmount()==null?orderBaseInfoDTO.getRealAmount():orderBaseInfoDTO.getPlatformAmount();
            orderBaseInfoDTO.setRstsAmount(basePrice);
            orderBaseInfoDTO.setRealAmount(basePrice);
            if (subOrderGroup.containsKey(orderBaseInfoDTO.getOrderNo())) {
                List<SubOrderDTO> sub=subOrderGroup.get(orderBaseInfoDTO.getOrderNo());
                for (SubOrderDTO subOrderDTO : sub) {
                    subOrderDTO.setMonthPay(orderBaseInfoDTO.getMonthPay());
                    if(RoDO.checkToPayAmount(orderBaseInfoDTO.getMonthPay(),subOrderDTO.getPayState(),orderBaseInfoDTO.getCurrency(),orderBaseInfoDTO.getState())) {
                        subOrderDTO.setToPayAmount(RoDO.getToPayAmountByOrder(amountMap, subOrderDTO.getOrderNo(),
                                subOrderDTO.getRealAmount(),null));
                    }
                    //给补充订单添加最后一次的记录

                    OrderOperatorLogDTO orderOperatorLogDTO=orderOperatorLogDomainService.getLastLog(orderBaseInfoDTO.getOrderNo()
                            ,Arrays.asList(OrderOperatorTypeEnum.CREATE_SUB_ORDER.getIndex()),null,subOrderDTO.getOrderNo());

                    subOrderDTO.setLastConfirmLog(orderOperatorLogDTO);
                }


                orderBaseInfoDTO.setSubOrderDTOList(sub);
                orderBaseInfoDTO.setRstsAmount(basePrice.add(subOrderService.getRstsAmount(orderBaseInfoDTO.getOrderNo())));
            }
            if (detailMap.containsKey(orderBaseInfoDTO.getOrderNo())) {
                orderBaseInfoDTO.setOrderDetailDTOList(detailMap.get(orderBaseInfoDTO.getOrderNo()));
            }
            if (sampleMap.containsKey(orderBaseInfoDTO.getOrderNo())) {
                orderBaseInfoDTO.setOrderSampleDTOList(sampleMap.get(orderBaseInfoDTO.getOrderNo()));
            }
            if(custInfoDTOMap.containsKey(orderBaseInfoDTO.getCustId())){
                orderBaseInfoDTO.setIndustryField("X1".equals(custInfoDTOMap.get(orderBaseInfoDTO.getCustId()))?1:0);
            }
            if(attrDTOMap.containsKey(orderBaseInfoDTO.getOrderNo())){
                orderBaseInfoDTO.setSampleCode(attrDTOMap.get(orderBaseInfoDTO.getOrderNo()));
            }
            if(attrDTOIdMap.containsKey(orderBaseInfoDTO.getOrderNo())){
                orderBaseInfoDTO.setSampleId(attrDTOIdMap.get(orderBaseInfoDTO.getOrderNo()));
            }


        }

        return orderBaseInfoDTOS;
    }

    @Override
    public List<UserCenterStateDTO> qryStateNumByVO(OrderToDoReq orderToDoReq, ConcurrentHashMap<String,String> map){
        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        OrderToDoVO orderToDoVO=orderBaseInfoDO.getOrderToDoVO(map,orderToDoReq.getUserDTO().getUserId());
        Map<String, EnumDTO> enumDTOMap=orderUtilHandle.getEnumByRedisKey(RedisKeyUtil.ORDER_STATE_NUM);
        if(orderToDoVO.getOrderFlg()==0 && orderToDoVO.getStoreFlg()==0){
            return  orderBaseInfoDO.qryUserCenterStateListByList(new HashMap<>(),orderToDoVO,enumDTOMap,0);
        }

        List<OrderBaseStateNumDTO> list= ConvertUtil.convertToTargetList(orderBaseInfoRepository.selectOrderBaseStateNumByVO(orderToDoVO),OrderBaseStateNumDTO.class);
        Map<Integer,List<OrderBaseStateNumDTO>> stateMap=list.stream().collect(Collectors.groupingBy(OrderBaseStateNumDTO::getOrderType));
        //缓存
        Map mapNum = new HashMap();
        mapNum.put(VOOrderBaseInfo.USER_ID, orderToDoReq.getUserDTO().getUserId());
        mapNum.put(SelectMapUtil.ORDER_TYPE, OrderTypeEnum.TIC.getIndex());
        mapNum.put(SelectMapUtil.IS_DELETE, 0);
        int t=orderBaseInfoCustomService.selectExpressNumState(mapNum);
        List<UserCenterStateDTO> returnList= orderBaseInfoDO.qryUserCenterStateListByList(stateMap,orderToDoVO,enumDTOMap,t);
        if(!ValidationUtil.isEmpty(returnList)){
            returnList=returnList.stream().sorted(Comparator.comparing(E->E.getState())).collect(Collectors.toList());
        }
        return returnList;
    }

    @Override
    public  PageInfo qryOrderToDoDTOByVO(OrderToDoReq orderToDoReq, ConcurrentHashMap<String,String> map) throws Exception{
        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        OrderToDoVO orderToDoVO=orderBaseInfoDO.getOrderToDoVO(map,orderToDoReq.getUserDTO().getUserId());
        orderToDoVO.setUserId(orderToDoReq.getUserDTO().getUserId());
        if(orderToDoVO.getOrderFlg()==0 && orderToDoVO.getStoreFlg()==0){
            return  new PageInfo(new ArrayList());
        }
        List<String> orderTypeList=new ArrayList<>();
        if(orderToDoVO.getTotalApplicationFlg()==1){
            orderTypeList.add(OrderTypeEnum.OIQ_PORTAL_INQUIRY.getIndex());
            orderTypeList.add(OrderTypeEnum.OIQ_PORTAL_ORDER.getIndex());
        }
        if(orderToDoVO.getTotalInquiryFlg()==1){
            orderTypeList.add(OrderTypeEnum.OIQORDER.getIndex());
            orderTypeList.add(OrderTypeEnum.OIQ_SUB_ORDER.getIndex());
        }
        if(orderToDoVO.getTotalStoreFlg()==1){
            orderTypeList.add(OrderTypeEnum.TIC.getIndex());
            orderTypeList.add(OrderTypeEnum.TIC_SUB.getIndex());
        }
        /*if(ValidationUtil.isEmpty(orderTypeList)){
            return  new PageInfo(new ArrayList());
        }*/
        orderTypeList.add(OrderTypeEnum.OIQINQUIRY.getIndex());
        orderToDoVO.setOrderTypeList(orderTypeList);
        PageHelper.startPage(orderToDoReq.getBasePage().getPageNum(), orderToDoReq.getBasePage().getPageRow());
        List<OrderBaseInfoExtend> extendList=orderBaseInfoRepository.qryOrderToDoDTOByVO(orderToDoVO);
        PageInfo pageInfo = new PageInfo(extendList);
        List<OrderToDoDTO> list=ConvertUtil.convertToTargetList(extendList,OrderToDoDTO.class);
        if(!ValidationUtil.isEmpty(list)){
            //获取todo的转义
            Map<String, EnumDTO> enumDTOMap=orderUtilHandle.getEnumByRedisKey(RedisKeyUtil.ORDER_TO_DO);
            for(int n=0;n<list.size();n++){
                OrderToDoDTO orderToDoDTO=list.get(n);
                EnumDTO enumDTO=enumDTOMap.getOrDefault(orderToDoDTO.getSortNum(),null);
                if(!ValidationUtil.isEmpty(enumDTO)){
                    String s1=OrderTypeEnum.TIC.getIndex().equals(OrderTypeEnum.getMainTypeBySub(String.valueOf(orderToDoDTO.getOrderType())))?orderLink:"";
                    orderToDoDTO.setMemo(String.format(enumDTO.getEnumName(),s1,orderToDoDTO.getOrderNo(),String.valueOf(orderToDoDTO.getOrderType())));
                    String ext= StrUtil.toStr(enumDTO.getEnumExtend());
                    String price=NumUtil.toFormat(orderToDoDTO.getRealAmount())+StrUtil.toStr(orderToDoDTO.getCurrency());
                    orderToDoDTO.setRemark(String.format(ext,s1,orderToDoDTO.getOrderNo(),String.valueOf(orderToDoDTO.getOrderType()),orderToDoDTO.getOrderNo(),price));
                }
            }
        }
        map.clear();
        pageInfo.setList(list);
        return pageInfo;
    }

    @Override
    public void quotationConfirm(BaseOrderDTO baseOrderDTO,String csCode) throws Exception{
        if(!baseOrderDTO.getOrderType().equals(OrderTypeEnum.OIQ_PORTAL_ORDER.getIndex())){
            return;
        }
        BaseOrderDTO inquiryDTO=selectBaseByOrderNo(baseOrderDTO.getRelateOrderNo());
        if(inquiryDTO.getState()==BaseOrderStateEnum.CONFIRM.getIndex()){
            return;
        }

        BusinessLineDTO businessLineDTO=centerRespository.qryBusinessLineDTOByLineId(baseOrderDTO.getLineId());
        if(ValidationUtil.isEmpty(businessLineDTO)){
            return;
        }

        String code= OrderUtil.getLogUserShow(baseOrderDTO);
        if(StringUtils.isNotBlank(csCode)){
            code=csCode;
        }
        if(StringUtils.isNotBlank(inquiryDTO.getPlatformOrder()) && BusinessCodeEnum.toDml(businessLineDTO.getPlatformCode())){
            openRestTemplateService.quotationConfirm(inquiryDTO.getPlatformOrder());
        }
        orderBaseInfoRepository.updateByPrimaryKeySelective(OrderBaseInfoDO.getOrderBaseInfoState(inquiryDTO.getOrderId(),4));
        //邮件发送通知客服
        mailEventUtil.sendMail(baseOrderDTO, OiqMailEnum.OIQ_PORTAL_QUOTATION_CONFIRM,2L);
        operatorLogDomainService.addLog(baseOrderDTO, OrderOperatorTypeEnum.DML_USER_CONFIRM_ORDER, code);
    }



    @Override
    public TicOrderReq checkShopCartData(TicOrderSaveReq ticOrderSaveReq, UserDTO userDTO, String token) throws Exception {
        LogUtil.writeMessage("用户通过新模式提交订单开始==>"+JSON.toJSONString(ticOrderSaveReq));
        //1.拿到缓存key中的信息
        TicOrderReq ticOrderReq = iTicRedisService.getOrder(ticOrderSaveReq.getTicKey(), userDTO);
        LogUtil.writeMessage("用户通过新模式提交订单缓存里面的数据开始==>"+JSON.toJSONString(ticOrderReq));
//        CheckTicUtil.checkTicOrder(ticOrderSaveReq,ticOrderReq);
        CheckTicUtil.checkSaveTicOrder(ticOrderSaveReq,ticOrderReq);
        //校验购物车信息
        ibbcsv.checkCart(ticOrderReq.getMode(),ticOrderReq.getMdCode(), token);
        return ticOrderReq;
    }

    @Override
    public TicOrderReq checkShopCartDataTest(TicOrderSaveReq ticOrderSaveReq, UserDTO userDTO, String token) throws Exception {
        LogUtil.writeMessage("用户通过新模式提交订单开始==>"+JSON.toJSONString(ticOrderSaveReq));
        //1.拿到缓存key中的信息
        TicOrderReq ticOrderReq = iTicRedisService.getOrder(ticOrderSaveReq.getTicKey(), userDTO);
        LogUtil.writeMessage("用户通过新模式提交订单缓存里面的数据开始==>"+JSON.toJSONString(ticOrderReq));
//        CheckTicUtil.checkTicOrder(ticOrderSaveReq,ticOrderReq);
        CheckTicUtil.checkSaveTicOrder(ticOrderSaveReq,ticOrderReq);
        //校验购物车信息
//        ibbcsv.checkCart(ticOrderReq.getMode(),ticOrderReq.getMdCode(), token);
        return ticOrderReq;
    }

    @Override
    public void appendOrderExtendByShared(List<OrderBaseInfoDTO> orderBaseInfoDTOList) {
        List<String> orderNoList=orderBaseInfoDTOList.stream().map(OrderBaseInfoDTO::getOrderNo).collect(Collectors.toList());
        //分享拼接样品数据
        appendOrderSample(orderBaseInfoDTOList,orderNoList);
        //分享拼接项目数据
        appendOrderDetail(orderBaseInfoDTOList,true);
        //分享添加申请方数据
        appendOrderApplication(orderBaseInfoDTOList,orderNoList);
        //分享添加报告数据
        appendOrderReport(orderBaseInfoDTOList,orderNoList);
        //分享添加报告文件数据
        appendFileList(orderBaseInfoDTOList,orderNoList,Arrays.asList(OrderAttachmentTypeEnum.OIQ_REPORT.getIndex()));
        // 拼接提单号
        appendLadingNo(orderBaseInfoDTOList, orderNoList);
    }

    @Override
    public void appendOrderExtendByOrderList(List<OrderBaseInfoDTO> orderBaseInfoDTOList) {
        List<String> orderNoList=orderBaseInfoDTOList.stream().map(OrderBaseInfoDTO::getOrderNo).collect(Collectors.toList());
        //列表拼接样品数据
        appendOrderSample(orderBaseInfoDTOList,orderNoList);
        //拼接项目数据
        appendOrderDetail(orderBaseInfoDTOList,true);
        //拼接申请方数据
        appendOrderApplication(orderBaseInfoDTOList,orderNoList);
        //拼接报告数据
        appendOrderReport(orderBaseInfoDTOList,orderNoList);
        //拼接发票，报告文件，报价单文件文件数据
        List<String> fileStrList=Arrays.asList(OrderAttachmentTypeEnum.OIQ_REPORT.getIndex(),OrderAttachmentTypeEnum.IND_QUOTATION_FILE.getIndex(),
                OrderAttachmentTypeEnum.OIQ_EXPRESS.getIndex());
        appendFileList(orderBaseInfoDTOList,orderNoList,fileStrList);
        //拼接补充订单相关数据
        appendSubOrder(orderBaseInfoDTOList,orderNoList);
        //拼接发票
        appendOrderInvoice(orderBaseInfoDTOList,orderNoList);
        //拼接询价单数据
        appendInquiry(orderBaseInfoDTOList);
        // 拼接提单号
        appendLadingNo(orderBaseInfoDTOList, orderNoList);
        //分享发票数据
        appendInvoiceTitle(orderBaseInfoDTOList,orderNoList);
    }




    @Override
    public Map<String, TicGroupProduct> getUserCouponForProductGroup(TicOrderSaveReq ticOrderSaveReq) {
        Map<String, TicGroupProduct> couponMap = new HashMap<>();
        if (ValidationUtil.isEmpty(ticOrderSaveReq.getTicCouponReqList())) {
            return couponMap;
        }
        List<TicCouponReq> ticCouponReqList = ticOrderSaveReq.getTicCouponReqList();

        ticCouponReqList.forEach(coupon -> {
            if (ValidationUtil.isEmpty(coupon.getGroupProductList())) {
                return;
            }
            coupon.getGroupProductList().forEach(group -> {
                couponMap.put(coupon.getStoreId() + "-" + group.getRow(), group);
            });
        });
        return couponMap;
    }

    @Override
    public void createShopOrderInfo(TicOrderSaveReq ticOrderSaveReq, UserDTO user,
                                UserPromotion promotion, UserCompanyDTO userCompany,
                                Map<String, TicGroupProduct> couponMap, List<String> orderNoList, Map<String, Object> baseMap,
                                List<ShopOrderSubmit> lstOrder) throws Exception {
        TicOrderReq shopOrderCache = ticOrderSaveReq.getShopOrderCache();
        if (ValidationUtil.isEmpty(shopOrderCache)) {
            throw new BusinessException(ResultEnumCode.REQ_ERROR);
        }
        List<TicStoreReq> ticStoreReqList = shopOrderCache.getStoreReqList();
        if (ValidationUtil.isEmpty(ticStoreReqList)) {
            throw new BusinessException(ResultEnumCode.REQ_ERROR);
        }

//        // 计算总金额, 按每个产品的 price计算（店铺优惠后金额）
//        BigDecimal bbcRealAmount = getOrderRealAmountByBbc(ticStoreReqList);

        //循环店铺
        for (TicStoreReq storeInfo: ticStoreReqList) {
            saveShopOrderByGroup( storeInfo, couponMap, lstOrder,ticOrderSaveReq, user,
                    userCompany, ValidationUtil.isEmpty(promotion) ? 0 : promotion.getState());
        }
    }


    private OrderBaseInfo generateShopOrder(TicOrderSaveReq ticOrderSaveReq, UserDTO user, UserCompanyDTO userCompany, Integer proState) {
        OrderBaseInfoDO domain = new OrderBaseInfoDO();
        OrderBaseInfo orderBaseInfo = domain.initBasic(user, ticOrderSaveReq.getOrderDate());
        domain.initOrderCustomer(orderBaseInfo, ticOrderSaveReq, userCompany);
        domain.initShopBasic(orderBaseInfo, proState);
        return orderBaseInfo;
    }

    private void appendShopInfo(OrderBaseInfo baseBean, TicGroupProduct ticGroupProduct) {
        OrderBaseInfoDO domain = new OrderBaseInfoDO();
        domain.initShopInfo(baseBean, ticGroupProduct);
        String orderNo = customCodeService.getTicOrderNo(ticGroupProduct.getStoreId());
        domain.setOrderNo(baseBean, orderNo);
    }

    /**
     * 拆单，需要复制BASE ORDER信息
     * @param ticGroupProduct
     * @param baseBean
     * @param colName
     * @throws Exception
     */
    private void dealSplitOrderByShop(TicGroupProduct ticGroupProduct, OrderBaseInfo baseBean,
                                      List<ShopOrderSubmit> lstOrder, TicGroupProduct groupProductCoupon, String colName) throws Exception {
        List<TicProductReq> lstProduct = ticGroupProduct.getProductDTOList();
        if (ValidationUtil.isEmpty(lstProduct)) {
            throw new BusinessException(ResultEnumCode.REQ_ERROR);
        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        int num = 0;
        // BBC金额计算
        for (TicProductReq product : lstProduct) {
            totalAmount = totalAmount.add(product.getTotalPrice());
            discountAmount = discountAmount.add(product.getShopDisAmount());
            num += product.getQuantity();
        }
        BigDecimal shopdisAmountAdd = BigDecimal.ZERO;//店铺累加金额
        BigDecimal totalAmountAdd = BigDecimal.ZERO;//
        for (int i=0;i<ticGroupProduct.getQuantity();i++) {
            OrderBaseInfo orderBaseInfo = ConvertUtil.convertToTarget(baseBean, OrderBaseInfo.class);
            appendShopInfo(orderBaseInfo, ticGroupProduct);
            // BBC店铺优惠肯定能整除，此处暂不考虑异常情况
            if(i == ticGroupProduct.getQuantity() -1){
                orderBaseInfo.setShopDisAmount(discountAmount.subtract(shopdisAmountAdd));
                orderBaseInfo.setOrderAmount(totalAmount.subtract(totalAmountAdd));
            }else {
                orderBaseInfo.setShopDisAmount(discountAmount.divide(new BigDecimal(num), 2, BigDecimal.ROUND_HALF_UP));
                shopdisAmountAdd = shopdisAmountAdd.add(discountAmount.divide(new BigDecimal(num), 2, BigDecimal.ROUND_HALF_UP));
                orderBaseInfo.setOrderAmount(totalAmount.divide(new BigDecimal(num), 2, BigDecimal.ROUND_HALF_UP));
                totalAmountAdd = totalAmountAdd.add(totalAmount.divide(new BigDecimal(num), 2, BigDecimal.ROUND_HALF_UP));
            }
            orderBaseInfo.setRealAmount(orderBaseInfo.getOrderAmount().subtract(orderBaseInfo.getShopDisAmount()));

            ShopOrderSubmit shopOrderSubmit = new ShopOrderSubmit(orderBaseInfo.getOrderNo(), groupProductCoupon.getReportNums(),
                    groupProductCoupon.getUrgentNums(), orderBaseInfo, lstProduct, colName, groupProductCoupon.getQuantity(),groupProductCoupon.getSubBuCode());
            addCoupon(shopOrderSubmit, ticGroupProduct, orderBaseInfo, groupProductCoupon);
            lstOrder.add(shopOrderSubmit);
        }
    }

    /**
     * 不拆单规则，直接存储信息
     * @param ticGroupProduct
     * @param baseBean
     * @param colName
     * @throws Exception
     */
    private void dealUnSplitOrderByShop(TicGroupProduct ticGroupProduct, OrderBaseInfo baseBean,
                                        List<ShopOrderSubmit> lstOrder, TicGroupProduct groupProductCoupon, String colName) throws Exception {
        List<TicProductReq> lstProduct = ticGroupProduct.getProductDTOList();
        if (ValidationUtil.isEmpty(lstProduct)) {
            throw new BusinessException(ResultEnumCode.REQ_ERROR);
        }

        // 不拆单，不需要copy基础订单信息
        appendShopInfo(baseBean, ticGroupProduct);

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        // BBC金额计算
        for (TicProductReq product : lstProduct) {
            totalAmount = totalAmount.add(product.getTotalPrice());
            discountAmount = discountAmount.add(product.getShopDisAmount());
            baseBean.setCategoryPath(product.getCategoryPath());
        }
        baseBean.setOrderAmount(totalAmount);
        baseBean.setShopDisAmount(discountAmount);
        baseBean.setRealAmount(baseBean.getOrderAmount().subtract(baseBean.getShopDisAmount()));

        ShopOrderSubmit shopOrderSubmit = new ShopOrderSubmit(baseBean.getOrderNo(), groupProductCoupon.getReportNums(),
                groupProductCoupon.getUrgentNums(), baseBean, lstProduct,colName,groupProductCoupon.getQuantity(),groupProductCoupon.getSubBuCode());
        addCoupon(shopOrderSubmit, ticGroupProduct, baseBean, groupProductCoupon);

        lstOrder.add(shopOrderSubmit);
    }

    private void addCoupon(ShopOrderSubmit shopOrderSubmit, TicGroupProduct ticGroupProduct,
                           OrderBaseInfo baseBean, TicGroupProduct groupProductCoupon) {
        List<BbcOrderCouponDTO> lstCoupon = shopOrderSubmit.getLstOrderCoupon();
        if (ValidationUtil.isEmpty(lstCoupon))
            lstCoupon = new ArrayList<>();
        Integer reportNumsAdd = ValidationUtil.isEmpty(groupProductCoupon.getReportNumsAdd())?0:groupProductCoupon.getReportNumsAdd();
        Integer urgentNumsAdd =  ValidationUtil.isEmpty(groupProductCoupon.getUrgentNumsAdd())?0:groupProductCoupon.getUrgentNumsAdd();
        //优惠券的使用扣减金额
        TicProductReq ticProductReq = ticGroupProduct.getProductDTOList().get(0);
        BbcOrderCouponDTO bbcOrderCouponDTO = new BbcOrderCouponDTO();
        bbcOrderCouponDTO.setOrderNo(baseBean.getOrderNo());
        bbcOrderCouponDTO.setStore(ticProductReq.getStoreId());
        bbcOrderCouponDTO.setStoreId(ticProductReq.getProdId() + "");
        bbcOrderCouponDTO.setAmount(baseBean.getRealAmount());
        //先判断报告总数量是否>增加的报告数量
            //是：说明报告券可用，直接加1
        if(groupProductCoupon.getReportNums()>reportNumsAdd){
            bbcOrderCouponDTO.setReportNums(1);
            reportNumsAdd +=1;
            groupProductCoupon.setReportNumsAdd(reportNumsAdd);
        }else {
            bbcOrderCouponDTO.setReportNums(0);
        }

        if(groupProductCoupon.getUrgentNums()>urgentNumsAdd){
            bbcOrderCouponDTO.setUrgentNums(1);
            urgentNumsAdd +=1;
            groupProductCoupon.setUrgentNumsAdd(urgentNumsAdd);
        }else {
            bbcOrderCouponDTO.setUrgentNums(0);
        }
        lstCoupon.add(bbcOrderCouponDTO);
        shopOrderSubmit.setLstOrderCoupon(lstCoupon);
    }

    private void saveShopOrderByGroup( TicStoreReq ticStoreReq, Map<String, TicGroupProduct> couponMap,
                                      List<ShopOrderSubmit> lstOrder,TicOrderSaveReq ticOrderSaveReq,
                                      UserDTO user,UserCompanyDTO userCompany, Integer state) throws Exception {
        List<TicGroupProduct> groupProductList = ticStoreReq.getGroupProductList();
        //循环分组
        for (int j = 0; j < groupProductList.size(); j++) {
            OrderBaseInfo baseBean = generateShopOrder(ticOrderSaveReq, user, userCompany, state);
            //1.拿到商品信息设置基本信息
            //判断当前商品下对应的申请表类型是哪一种
            TicGroupProduct ticGroupProduct = groupProductList.get(j);
            String subBuCode = ticGroupProduct.getSubBuCode();
            String colName = redisUtils.getSplitOrderRuler(subBuCode);//获取到当前的这个申请表拆单规则是在那个分类里面

            if (!ValidationUtil.isEmpty(ticGroupProduct.getLabId())) {
                LabDTO lab = ticOrderSaveReq.getLabMap().getOrDefault(ticGroupProduct.getLabId(), null);
                if (ValidationUtil.isEmpty(lab)){
                    lab = centerRestTemplateService.qryLabInfoByLabId(ticGroupProduct.getLabId());
                }
                baseBean.setLabId(lab.getLabId());
                baseBean.setLabName(lab.getLabName());
                baseBean.setCatagoryId(lab.getCategoryId());
                ticOrderSaveReq.getLabMap().put(ticGroupProduct.getLabId(), lab);
            }else {
                throw new BusinessException(ResultEnumCode.LAB_ERROR);
            }

            // 优惠券
            TicGroupProduct groupProductCoupon = couponMap.getOrDefault(ticGroupProduct.getStoreId() + "-" + ticGroupProduct.getRow(), new TicGroupProduct());


            //modify by shenyi 2024-06-23 只有 buynums 才拆单，其他的都不用拆单根据group走
            if (!CreateOrderSplitType.byNums.getName().equals(colName)) {
                dealUnSplitOrderByShop(ticGroupProduct, baseBean, lstOrder, groupProductCoupon,colName);
            } else {
                dealSplitOrderByShop(ticGroupProduct, baseBean, lstOrder, groupProductCoupon,colName);
            }

        }
    }

    @Override
    public VOOrderBaseInfo saveOrderInfoByCreate(UserDTO userDTO, UserPromotion promotion, String orderSource, Map<String, Object> orderUseCoupon, Map<String, Object> getBaseMap, TicStoreReq ticStoreReq, TicProductReq ticProductReq, Boolean numFlg, int n) {
        VOOrderBaseInfo voOrderBaseInfoAdd = (VOOrderBaseInfo) getBaseMap.get(ticProductReq.getRow()+"-"+ n);
        voOrderBaseInfoAdd.setOrderType(Integer.parseInt(OrderTypeEnum.TIC.getIndex()));
        voOrderBaseInfoAdd.setBu(ticStoreReq.getStoreId());
        voOrderBaseInfoAdd.setCategoryPath(ticProductReq.getCategoryPath());
        voOrderBaseInfoAdd.setCatagoryId(ticProductReq.getCategoryId());
        voOrderBaseInfoAdd.setOrderAmount(numFlg ? ticProductReq.getOriginalPrice():

                ticProductReq.getOriginalPrice().multiply(new BigDecimal(ticProductReq.getQuantity())));

        if(orderUseCoupon.containsKey(voOrderBaseInfoAdd.getOrderNo())){
            BbcOrderCouponDTO bbcOrderCouponDTO=(BbcOrderCouponDTO) orderUseCoupon.get(voOrderBaseInfoAdd.getOrderNo());
            voOrderBaseInfoAdd.setDiscountAmount(bbcOrderCouponDTO.getDiscountAmount());
            voOrderBaseInfoAdd.setRealAmount(voOrderBaseInfoAdd.getRealAmount().subtract(voOrderBaseInfoAdd.getDiscountAmount()));
        }

        voOrderBaseInfoAdd.setOrderSource(orderSource);
        //
        //add begin by bowen.zhang 查询当前会员状态并且实例化数据
        if(!ValidationUtil.isEmpty(promotion)){
            voOrderBaseInfoAdd.setProState(promotion.getState());
        }

        if(userDTO.getIsTest()==1){
            voOrderBaseInfoAdd.setIsTest(1);
        }
        orderBaseInfoMapper.insertVOSelective(voOrderBaseInfoAdd);
        return voOrderBaseInfoAdd;
    }

    @Override
    public void saveShopOrderSubmit(List<ShopOrderSubmit> lstOrder, Map<String, Object> couponMapResult, Map<Long, LabDTO> labMap,
                                    List<String> orderNoList, LinkedHashSet<String> buList, HashSet<String> accountSet) throws Exception {
        List<VOOrderApplicationAttr> list = new ArrayList<>();

        for (ShopOrderSubmit orderSubmit : lstOrder) {
            List<OrderProductDTO> listProduct = new ArrayList<>();//用以发送邮件
            List<OrderDetailDTO> listDetail=  new ArrayList<>();//用以发送邮件
            OrderBaseInfo order = orderSubmit.getOrderBaseInfo();
            order.setGroupNo(customCodeService.getNewGroupNo());
            orderSubmit.setGroupNo(order.getGroupNo());
            orderNoList.add(order.getOrderNo());
            buList.add(order.getBu());
            if(!ValidationUtil.isEmpty(couponMapResult.get(order.getOrderNo()))){
                BbcOrderCouponDTO bbcOrderCouponDTO=(BbcOrderCouponDTO) couponMapResult.get(order.getOrderNo());
                order.setDiscountAmount(bbcOrderCouponDTO.getDiscountAmount());
                order.setRealAmount(order.getRealAmount().subtract(order.getDiscountAmount()));
            }

            // 商品信息
            List<OrderProduct> productList = orderProductDomainService.saveShopProduct(orderSubmit.getLstProduct(), order, orderSubmit.getColName(), orderSubmit.getNums(), listProduct, listDetail);
            // sub状态
            if (ValidationUtil.isEmpty(order.getSubState())) {
                order.setSubState(OrderSubStateEnum.NO_OPERATOR.getIndex());
            }
            orderBaseInfoMapper.insertReturnKey(order);

            //设置当前订单的语言字段
            disposeOrderUpLua(productList,orderSubmit);

            //处理实验室信息
            saveOrderApplicationToLab(order, labMap,list,accountSet);
            //保存日志
            saveOrderLog(order);
            //发送邮件短信
            sendMailAndSms(orderSubmit.getLstProduct(),order,listProduct,listDetail);
        }

        orderApplicationAttrService.insertForeach(list);
    }

    private void disposeOrderUpLua(List<OrderProduct> productList, ShopOrderSubmit orderSubmit) {

        if(ValidationUtil.isEmpty(productList))
            return;

        String skuAttr = productList.get(0).getSkuAttr();

        List<BbcSkuAttrDTO> skuAttrDTOList = JSON.parseArray(skuAttr, BbcSkuAttrDTO.class);
        String lua = "";
        if (!ValidationUtil.isEmpty(skuAttrDTOList)) {
            for (BbcSkuAttrDTO bbcSkuAttrDTO : skuAttrDTOList) {
                if (BbcSkuAttrEnum.REPORT_LUA.getName().equals(bbcSkuAttrDTO.getAttrKey())) {
                    String reportLuaCode = !ValidationUtil.isEmpty(bbcSkuAttrDTO.getAttrValueCode()) ? bbcSkuAttrDTO.getAttrValueCode() :
                            (!ValidationUtil.isEmpty(ReportLuaEnum.getNameByNameCh(bbcSkuAttrDTO.getAttrValue())) ?
                                    ReportLuaEnum.getNameByNameCh(bbcSkuAttrDTO.getAttrValue()) :
                                    CertificateLuaEnum.getNameByNameCh(bbcSkuAttrDTO.getAttrValue()));
                    if(ValidationUtil.isEmpty(lua) && !ValidationUtil.isEmpty(reportLuaCode)){//没有语言，并且报告语言不为空时
                        lua = ReportLuaCodeMappingBasicEnum.getIndex(reportLuaCode.toUpperCase());
                    }
                }
            }
        }

        orderSubmit.setLua(ValidationUtil.isEmpty(lua) ? ReportLuaCodeMappingBasicEnum.CHI.getIndex() :lua);//默认设置中文
    }

    @Override
    public OrderBaseInfoDTO qryOrderBase(VOOrderBaseInfo voOrderBaseInfo) {
        OrderBaseInfo orderBaseInfo = orderBaseInfoRepository.qryOrderBase(voOrderBaseInfo);
        return ConvertUtil.convertToTarget(orderBaseInfo, OrderBaseInfoDTO.class);
    }

    @Override
    public void appendForm(OrderBaseInfoDTO orderBaseInfoDTO, Map<String, List<OrderAttachmentDTO>> listMap, String type, List<OrderBaseInfoDTO> subOrderDTOList) {
        List<OrderAttachmentDTO> attachmentDTOList = listMap.get(OrderAttachmentTypeEnum.MSDS_EXCEL_FILE.getIndex());
        //时间
        OrderOperatorLogDTO lastOrderLog = iOrderLogService.getLastOrderLog(orderBaseInfoDTO.getOrderNo(), OrderOperatorTypeEnum.FORM.getIndex());
        orderBaseInfoDTO.setSubmitApplicationDate(!ValidationUtil.isEmpty(lastOrderLog) ? lastOrderLog.getOperatorDate() : "");
        if(!ValidationUtil.isEmpty(attachmentDTOList)){
            orderBaseInfoDTO.setMsdsFileNames(attachmentDTOList.stream().map(OrderAttachmentDTO::getFileName).collect(Collectors.joining(",")));
        }
        BigDecimal subRealAmount = BigDecimal.ZERO;
        if(!ValidationUtil.isEmpty(subOrderDTOList)){//
            subRealAmount = subOrderDTOList.stream().map(OrderBaseInfoDTO::getRealAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if(OrderPrintEnum.QUOTATION.getIndex().equals(type)){//报价单
            orderBaseInfoDTO.setShopDisAmount(BigDecimal.ZERO.subtract(NumUtil.toZero(orderBaseInfoDTO.getShopDisAmount())));
            orderBaseInfoDTO.setDiscountAmount(BigDecimal.ZERO.subtract(NumUtil.toZero(orderBaseInfoDTO.getDiscountAmount())));
            orderBaseInfoDTO.setCsDiscountAmount(BigDecimal.ZERO.subtract(NumUtil.toZero(orderBaseInfoDTO.getCsDiscountAmount())));
        }
        orderBaseInfoDTO.setServiceAmount(ValidationUtil.isEmpty(orderBaseInfoDTO.getServiceAmount())?BigDecimal.ZERO:orderBaseInfoDTO.getServiceAmount());
        orderBaseInfoDTO.setRealTotalAmount(orderBaseInfoDTO.getRealAmount().add(NumUtil.toZero(subRealAmount)));
    }

    private void sendMailAndSms(List<TicProductReq> lstProduct, OrderBaseInfo order, List<OrderProductDTO> listProduct, List<OrderDetailDTO> listDetail) {
        List<String> buList = new ArrayList();
        List<VOOrderProduct> sendProductList =  copyList(lstProduct);
        buList.add(order.getBu());
        VOOrderBaseInfo voOrderBaseInfo =  copyBaseOrderInfo(order);
        VOOrderApplicationForm voOrderApplicationForm = getVoOrderApplicationForm(order);
        String personForMsg = sendMailForCsGroup.qryPersonForMsg(Integer.valueOf(OiqMailEnum.TIC_CREATE_ORDER.getIndex()), order.getLabId(), "MESSAGE");
        eventMailUtil.createOrder(new CreateOrderSendDTO(voOrderBaseInfo,voOrderApplicationForm,sendProductList,listProduct,listDetail,personForMsg));
        eventSmsUtil.sendSMSCreate(new CreateOrderSendDTO(voOrderBaseInfo,voOrderApplicationForm,sendProductList));
    }

    private VOOrderApplicationForm getVoOrderApplicationForm(OrderBaseInfo order) {
        VOOrderApplicationForm voOrderApplicationForm= new VOOrderApplicationForm();

        voOrderApplicationForm.setLinkPerson(order.getUserName());
        voOrderApplicationForm.setLinkPhone(order.getUserPhone());
        voOrderApplicationForm.setLinkEmail(order.getUserEmail());
        return voOrderApplicationForm;
    }

    private VOOrderBaseInfo copyBaseOrderInfo(OrderBaseInfo order) {
        VOOrderBaseInfo voOrderBaseInfo = new VOOrderBaseInfo();
        baseCopyObj.copy(voOrderBaseInfo,order);
        return voOrderBaseInfo;
    }

    private List<VOOrderProduct> copyList(List<TicProductReq> lstProduct) {
        List<VOOrderProduct> sendProductList=new ArrayList();
        lstProduct.stream().forEach(ticProductReq -> {
            VOOrderProduct voOrderProduct = new VOOrderProduct();
            baseCopyObj.copy(voOrderProduct,ticProductReq);
            sendProductList.add(voOrderProduct);

        });
        return sendProductList;
    }


    private void saveOrderLog(OrderBaseInfo order) {
        orderOperatorLogService.addLogByBase(new BaseLog(order.getOrderNo(),String.valueOf(order.getOrderType()),
                OrderOperatorTypeEnum.CREATE_ORDER, null, null, order.getUserName(), 1));

        if(order.getIsTest()==1){
            orderOperatorLogService.addLogByBase(new BaseLog(order.getOrderNo(),String.valueOf(order.getOrderType()),
                    OrderOperatorTypeEnum.ISTEST, "测试订单", null, "系统", 1));
    }


    }

    private void saveOrderApplicationToLab(OrderBaseInfo order, Map<Long, LabDTO> labMap, List<VOOrderApplicationAttr> list, HashSet<String> accountSet) {
        LabDTO labDTO = labMap.getOrDefault(order.getLabId(), new LabDTO());
        StringJoiner SJ = new StringJoiner(",");
        SJ.add(ValidationUtil.isEmpty(labDTO)?"":ValidationUtil.isEmpty(labDTO.getAccountNo()) || labDTO.getAccountNo().equals("disables")?"":labDTO.getAccountNo());
        SJ.add(ValidationUtil.isEmpty(labDTO)?"":ValidationUtil.isEmpty(labDTO.getWxpayChannel()) || labDTO.getWxpayChannel().equals("disables")?"":labDTO.getWxpayChannel());
        SJ.add(ValidationUtil.isEmpty(labDTO)?"":ValidationUtil.isEmpty(labDTO.getAlipayChannel()) || labDTO.getAlipayChannel().equals("disables")?"":labDTO.getAlipayChannel());
        accountSet.add(SJ.toString());
        Map<String, Object> companyMap = JSON.parseObject(JSON.toJSONString(labDTO), Map.class);
        for (String companyKey : companyMap.keySet()) {
            VOOrderApplicationAttr voOrderApplicationAttr=new VOOrderApplicationAttr();
            voOrderApplicationAttr.setAreaCode(AppFormCodeEnum.LAB_INFO.getName());
            voOrderApplicationAttr.setCreateDate(UseDateUtil.getDateString(new Date()));
            voOrderApplicationAttr.setAttrCode(companyKey);
            voOrderApplicationAttr.setAttrName(companyKey);
            voOrderApplicationAttr.setAttrValue(companyMap.get(companyKey).toString());
            voOrderApplicationAttr.setOrderNo(order.getOrderNo());
            voOrderApplicationAttr.setState(1);
            list.add(voOrderApplicationAttr);
        }
    }

    @Override
    public void checkOrderUser(String orderNo, Long userId) {
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoMapper.checkOrderBase(orderNo);

        if (ValidationUtil.isEmpty(orderBaseInfoCheckDTO)) {
            throw new BusinessException(ResultEnumCode.ORDER_NULL);
        }

        if (!CheckUtil.isSame(userId, orderBaseInfoCheckDTO.getUserId())) {
            throw new BusinessException(ResultEnumCode.USER_IS_NULL);
        }
    }

    @Override
    public List<OrderBaseInfoDTO> getShopRelateOrderByOrder(String orderNo) {
        List<OrderBaseInfo> lstOrder = orderBaseInfoRepository.qryRelateOrderByOrder(orderNo,
                OrderTypeEnum.transIndexToInteger(OrderTypeEnum.TIC_SUB.getIndex()));
        return ConvertUtil.convertToTargetList(lstOrder, OrderBaseInfoDTO.class);
    }

    @Override
    public OrderSubInfoDTO generatePayAggregationByOrder(String orderNo, List<OrderBaseInfoDTO> lstSubOrder) {
        int subUnPayNum = 0;
        BigDecimal subUnPayAmount = BigDecimal.ZERO;
        int subPayNum = 0;
        BigDecimal subPayAmount = BigDecimal.ZERO;
        for (OrderBaseInfoDTO subOrder : lstSubOrder) {
            if (OrderStateEnum.CANCEL_STATE_LIST.contains(subOrder.getState() + "") ) {
                continue;
            }
            if (subOrder.getPayState() != 1) {
                subUnPayNum++;
                subUnPayAmount = subUnPayAmount.add(subOrder.getRealAmount());
            } else {
                subPayNum++;
                subPayAmount = subPayAmount.add(subOrder.getRealAmount());
            }
        }
        OrderSubInfoDTO payment = new OrderSubInfoDTO();
        payment.setOrderNo(orderNo);
        payment.setSubPayNum(subPayNum);
        payment.setSubPayPrice(subPayAmount);
        payment.setSubUnPayNum(subUnPayNum);
        payment.setSubUnPayPrice(subUnPayAmount);
        return payment;
    }

    @Override
    public List<OrderBaseInfoDTO> qrySubOrderList(Map<String, Object> subMap) {
        return orderBaseInfoRepository.qrySubOrderList(subMap);
    }

    @Override
    public List<OrderBaseInfoDTO> getNormalSubOrderList(List<OrderBaseInfoDTO> subOrderDTOList, OrderBaseInfoDTO orderBaseInfoDTO) {
        if(!ValidationUtil.isEmpty(subOrderDTOList)){
            List<OrderBaseInfoDTO> collect = subOrderDTOList.stream()
                    .filter(subOrderDTO -> subOrderDTO.getState() != Integer.parseInt(OrderStateEnum.CANCELLED.getIndex()))
                    .collect(Collectors.toList());

            if(ValidationUtil.isEmpty(collect)){
                orderBaseInfoDTO.setSubOrderPrice(BigDecimal.ZERO);
                return collect;
            }
            orderBaseInfoDTO.setSubOrderPrice(collect.stream().map(OrderBaseInfoDTO::getRealAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            return collect;
        }else {
            orderBaseInfoDTO.setSubOrderPrice(BigDecimal.ZERO);
        }
        return subOrderDTOList;
    }



    @Override
    public void updateOrderByInquiry(String orderNo, String qryOrder) {
        BaseOrderDTO inquiryDTO=orderBaseInfoRepository.selectBaseByOrderNo(orderNo);
        BaseOrderDTO orderDTO=orderBaseInfoRepository.selectBaseByOrderNo(qryOrder);
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        orderBaseInfo.setOrderId(orderDTO.getOrderId());
        orderBaseInfo.setOrderAmount(inquiryDTO.getOrderAmount());
        orderBaseInfo.setRealAmount(inquiryDTO.getRealAmount());
        orderBaseInfo.setDiscountAmount(inquiryDTO.getDiscountAmount());
        orderBaseInfo.setUrgentAmount(inquiryDTO.getUrgentAmount());
        orderBaseInfo.setServiceAmount(inquiryDTO.getServiceAmount());
        orderBaseInfo.setCurrency(inquiryDTO.getCurrency());
        orderBaseInfo.setStateDate(UseDateUtil.getDateString(new Date()));
        orderBaseInfoRepository.updateByPrimaryKeySelective(orderBaseInfo);
    }


    private SampleCategoryDTO getSampleCategoryBySampleList(List<OiqSampleDTO> oiqSampleDTOList, String businessCode, String bu, Long lineId)
        throws Exception {
        SampleCategoryDTO sampleCategoryDTO=OrderSampleDO.listToSampleCategory(oiqSampleDTOList);
        Map<String, String> sampleCategoryMap = centerService.qrySampleCategory(bu, lineId);
        sampleCategoryDTO.setSampleCategoryName(sampleCategoryMap.get(sampleCategoryDTO.getSampleCategoryCode()));
        sampleCategoryDTO.setSampleShapeName(orderUtilService.getEnumStringByKey(RedisKeyUtil.MIN_SAMPLE_SHAPE + businessCode,sampleCategoryDTO.getSampleShapeCode()));
        return sampleCategoryDTO;
    }



    /**
     * 订单列表拼接样品的数据
     * @param orderBaseInfoDTOList
     * @param orderNoList
     * @return void
     * <AUTHOR> || created at 2025/2/6 9:14
     * @throws Exception 抛出错误
     */
    private void appendOrderSample(List<OrderBaseInfoDTO> orderBaseInfoDTOList, List<String> orderNoList) {
        List<OrderSampleStrDTO> sampleStrDTOList=orderSampleDomainService.qryOrderSampleStrList(orderNoList);
        Map<String,List<OrderSampleStrDTO>> sampleStrDTOMap=sampleStrDTOList.stream().collect(Collectors.groupingBy(E -> E.getOrderNo()));
        // 循环订单数据拼接样品信息
        for (OrderBaseInfoDTO order: orderBaseInfoDTOList) {
            order.setOrderSampleStrDTOList(sampleStrDTOMap.getOrDefault(order.getOrderNo(),new ArrayList<>()));
        }
    }

    private void appendOrderInvoice(List<OrderBaseInfoDTO> orderBaseInfoDTOList, List<String> orderNoList) {
        Map<String, Integer> mapInt = orderInvoiceDomainService.selectInvoiceTypeMap(orderNoList);
        for (OrderBaseInfoDTO order: orderBaseInfoDTOList) {
            order.setInvoiceType(mapInt.getOrDefault(order.getOrderNo(),0));
        }
    }

    /**
    * 分享拼接项目
    * @param orderBaseInfoDTOList
    * @param hasAddOiq
    * @return void
    * <AUTHOR> || created at 2025/2/6 9:43
    * @throws Exception 抛出错误
    */
    private void appendOrderDetail(List<OrderBaseInfoDTO> orderBaseInfoDTOList, Boolean hasAddOiq){
        //订单号加groupNo
        List<String> orderAddGroup = orderBaseInfoDTOList.stream().map(a -> a.getOrderNo() + '-' + a.getGroupNo()).collect(Collectors.toList());
        // 如需要加载询价单的测试项目，则获取询价单列表
        List<String> inquiryAddGroup =new ArrayList<>();
        Map<String,String> relateMap=new HashMap<>();

        if (hasAddOiq) {
            List<String> inquiryNoList=orderBaseInfoDTOList.stream().filter(a-> StringUtils.isNotBlank(a.getRelateOrderNo())).
                    map(OrderBaseInfoDTO::getRelateOrderNo).collect(Collectors.toList());
            //询价单不为空的时候 且有groupNo的时候
            if(!ValidationUtil.isEmpty(inquiryNoList)){
                //查询询价单的数据
                List<OrderBaseInfoDTO> inquiryList=qryList(new OrderBaseInfoVO(inquiryNoList));
                inquiryAddGroup = inquiryList.stream().filter(a-> StringUtils.isNotBlank(a.getGroupNo())).
                        map(a -> a.getOrderNo() + '-' + a.getGroupNo()).collect(Collectors.toList());
                //询价单与订单的对应关系
                relateMap=orderBaseInfoDTOList.stream().filter(a-> StringUtils.isNotBlank(a.getRelateOrderNo()))
                        .collect(Collectors.toMap(E->E.getRelateOrderNo(), E->E.getOrderNo(), (key1, key2) -> key2));
            }
        }

        Map<String, List<OrderDetailDTO>> map = orderDetailDomainService.qryDetailListByInquiryOrOrderGroup(orderAddGroup,inquiryAddGroup,relateMap);
        for (OrderBaseInfoDTO orderBaseInfoDTO: orderBaseInfoDTOList) {
            // 获取订单的测试项目
            List<OrderDetailDTO> details = map.getOrDefault(orderBaseInfoDTO.getOrderNo(),new ArrayList<>());
            // 拼接信息
            orderBaseInfoDTO.setOrderDetailSize(details.size());
            orderBaseInfoDTO.setOrderDetailStr(details.stream().filter(item -> StringUtils.isNotBlank(item.getItemName())).map(OrderDetailDTO::getItemName).collect(Collectors.joining("、")));

        }
    }

    /**
    * 拼接申请表数据
    * @param orderBaseInfoDTOList
    * @param orderNoList
    * @return void
    * <AUTHOR> || created at 2025/2/6 14:03
    * @throws Exception 抛出错误
    */
    private void appendOrderApplication(List<OrderBaseInfoDTO> orderBaseInfoDTOList,List<String> orderNoList){
        Map<String, OrderApplicationFormDTO> applicationMap = orderApplicationFormDomainService.selectFormMapByOrderNoList(orderNoList);
        // 循环订单数据拼接申请表信息
        for (OrderBaseInfoDTO order: orderBaseInfoDTOList) {
            if(applicationMap.containsKey(order.getOrderNo())){
                OrderApplicationFormDTO applicationForm=applicationMap.get(order.getOrderNo());
                String nameStr=StringUtils.isBlank(applicationForm.getCompanyNameCn())? applicationForm.getCompanyNameEn()
                        : applicationForm.getCompanyNameCn();
                order.setApplicationCompanyNameStr(nameStr);
                order.setHaveForm(applicationMap.containsKey(order.getOrderNo())?1:0);
            }
        }
    }

    /**
    * 拼接报告对象数据
    * @param orderBaseInfoDTOList
    * @param orderNoList
    * @return void
    * <AUTHOR> || created at 2025/2/6 16:20
    * @throws Exception 抛出错误
    */
    private void appendOrderReport(List<OrderBaseInfoDTO> orderBaseInfoDTOList,List<String> orderNoList){

        Map<String, OrderReportDTO> reportDTOMap=orderReportDomainService.qryOrderReportByOrderNo(orderNoList);
        // 循环订单数据拼接报告表信息
        for (OrderBaseInfoDTO order: orderBaseInfoDTOList) {
            order.setReport(new OrderReportDTO());
            if(reportDTOMap.containsKey(order.getOrderNo())){
                OrderReportDTO orderReportDTO=reportDTOMap.get(order.getOrderNo());
                String nameStr=StringUtils.isBlank(orderReportDTO.getReportCompanyNameCn())? orderReportDTO.getReportCompanyNameEn()
                        : orderReportDTO.getReportCompanyNameCn();
                order.setReportCompanyNameStr(nameStr);
                order.setReport(reportDTOMap.getOrDefault(order.getOrderNo(),new OrderReportDTO()));
            }
        }

    }

    /***
    * 拼接附件文件
    * @param orderBaseInfoDTOList
    * @param orderNoList
    * @return void
    * <AUTHOR> || created at 2025/2/6 16:26
    * @throws Exception 抛出错误
    */
    private void appendFileList(List<OrderBaseInfoDTO> orderBaseInfoDTOList,List<String> orderNoList,List<String> addFileList){

        Map<String, List<OrderAttachmentDTO>> fileListMap = orderAttachmentDomainService.selectAttachmentMapByOrderNoList(orderNoList);
        for (OrderBaseInfoDTO orderBaseInfoDTO: orderBaseInfoDTOList) {
            if(addFileList.contains(OrderAttachmentTypeEnum.OIQ_REPORT.getIndex()) && OrderUtil.isShowReport(orderBaseInfoDTO.getPayState(),orderBaseInfoDTO.getState(),orderBaseInfoDTO.getIsPayReceived())){
                orderBaseInfoDTO.setReportFileList(OrderAttachmentDO.getFileListByAttType(fileListMap,orderBaseInfoDTO.getOrderNo(),OrderAttachmentTypeEnum.OIQ_REPORT));
            }
            if(addFileList.contains(OrderAttachmentTypeEnum.IND_QUOTATION_FILE.getIndex())){
                orderBaseInfoDTO.setQuotationFileList(OrderAttachmentDO.getFileListByAttType(fileListMap,orderBaseInfoDTO.getOrderNo(), OrderAttachmentTypeEnum.IND_QUOTATION_FILE));
            }
            if(addFileList.contains(OrderAttachmentTypeEnum.OIQ_EXPRESS.getIndex())){
                orderBaseInfoDTO.setInvoiceFileList(OrderAttachmentDO.getFileListByAttType(fileListMap,orderBaseInfoDTO.getOrderNo(), OrderAttachmentTypeEnum.OIQ_EXPRESS));
            }
        }
    }

    /**
    * 拼接补充订单数据
    * @param orderBaseInfoDTOList
    * @param orderNoList
    * @return void
    * <AUTHOR> || created at 2025/2/11 11:23
    * @throws Exception 抛出错误
    */
    private void appendSubOrder(List<OrderBaseInfoDTO> orderBaseInfoDTOList,List<String> orderNoList){
        //补充订单数据
        List<SubOrderDTO> subOrderDTOList = subOrderService.selectListByOrderList(orderNoList);
        //补充订单的未支付的数量和金额
        List<SubOrderInfoDTO> subOrderInfoDTOList=subOrderDomainService.getSubToPayNumByOrderNoList(orderNoList);
        //对应map
        Map<String, List<SubOrderDTO>> subOrderGroup = subOrderDTOList.stream().collect(Collectors.groupingBy(E -> E.getRelateOrderNo()));
        Map<String, List<SubOrderInfoDTO>> subOrderInfoGroup = subOrderInfoDTOList.stream().collect(Collectors.groupingBy(E -> E.getOrderNo()));

        Map<String,String> currencyMarkMap= orderUtilService.getCurrencyMark(RedisKeyUtil.OIQ_CURRENCY);
        List<String> subOrderNoList=subOrderDTOList.stream().map(SubOrderDTO::getOrderNo).collect(Collectors.toList());
        //补充订单的详情数据
        List<OrderDetailDTO> subDetailList=orderDetailDomainService.selectSubDetailListByOrderList(subOrderNoList);
        Map<String, List<OrderDetailDTO>> subDetailGroup = subDetailList.stream().collect(Collectors.groupingBy(E -> E.getOrderNo()));
        for (OrderBaseInfoDTO orderBaseInfoDTO: orderBaseInfoDTOList) {
            orderBaseInfoDTO.setCurrencyMark(currencyMarkMap.getOrDefault(orderBaseInfoDTO.getCurrency(),""));
            if (subOrderGroup.containsKey(orderBaseInfoDTO.getOrderNo())) {
                List<SubOrderDTO> subList=subOrderGroup.get(orderBaseInfoDTO.getOrderNo());
                for(SubOrderDTO subOrderDTO:subList){
                    subOrderDTO.setCurrencyMark(currencyMarkMap.getOrDefault(orderBaseInfoDTO.getCurrency(),""));
                    subOrderDTO.setOrderDetailDTOList(subDetailGroup.getOrDefault(subOrderDTO.getOrderNo(),new ArrayList<>()));
                }
                orderBaseInfoDTO.setSubOrderDTOList(subList);
            }
            if(subOrderInfoGroup.containsKey(orderBaseInfoDTO.getOrderNo()) && subOrderInfoGroup.get(orderBaseInfoDTO.getOrderNo()).size()>0){
                orderBaseInfoDTO.setSubOrderInfoDTO(subOrderInfoGroup.get(orderBaseInfoDTO.getOrderNo()).get(0));
            }
        }
    }

    /**
    * 拼接询报价的数据
    * @param orderBaseInfoDTOList
    * @return void
    * <AUTHOR> || created at 2025/2/11 11:44
    * @throws Exception 抛出错误
    */
    private void appendInquiry(List<OrderBaseInfoDTO> orderBaseInfoDTOList){

        List<String> inquiryNoList=orderBaseInfoDTOList.stream().filter(a-> StringUtils.isNotBlank(a.getRelateOrderNo())).
                map(OrderBaseInfoDTO::getRelateOrderNo).collect(Collectors.toList());

        if(ValidationUtil.isEmpty(inquiryNoList)){
            return;
        }
        Map<String,String> relateMap=orderBaseInfoDTOList.stream().filter(a-> StringUtils.isNotBlank(a.getRelateOrderNo()))
                .collect(Collectors.toMap(E->E.getOrderNo(), E->E.getRelateOrderNo(), (key1, key2) -> key2));
        List<OrderBaseInfoDTO> inquiryList=qryList(new OrderBaseInfoVO(inquiryNoList));
        Map<String,OrderBaseInfoDTO> inquiryMap=inquiryList.stream().collect(Collectors.toMap(OrderBaseInfoDTO::getOrderNo, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));

        for (OrderBaseInfoDTO orderBaseInfoDTO: orderBaseInfoDTOList) {
            if(relateMap.containsKey(orderBaseInfoDTO.getOrderNo())){
                if(inquiryMap.containsKey(relateMap.get(orderBaseInfoDTO.getOrderNo()))){
                    OrderBaseInfoDTO inquiryDTO=inquiryMap.get(relateMap.get(orderBaseInfoDTO.getOrderNo()));
                    orderBaseInfoDTO.setInquiryBaseDTO(new PortalInquiryBaseDTO(inquiryDTO.getOrderNo(),inquiryDTO.getState(),0));
                }
            }
        }
    }

    /**
     * 拼接提单号数据
     *
     * @param orderBaseInfoDTOList
     * @param orderNoList
     */
    private void appendLadingNo(List<OrderBaseInfoDTO> orderBaseInfoDTOList, List<String> orderNoList) {
        Map<String,Object> map = new HashMap<>();
        map.put(SelectListUtil.ORDER_NO_LIST,orderNoList);
        map.put(SelectMapUtil.STATE, 1);
        List<OrderShippingDTO> orderShippingDTOList = orderShippingMapper.selectListByMap(map);

        Map<String, String> orderShippingLadingNoMap = orderShippingDTOList.stream()
                .filter(Objects::nonNull)
                .filter(item -> !ValidationUtil.isEmpty(item.getLadingNo()))
                .collect(Collectors.toMap(OrderShippingDTO::getOrderNo, OrderShippingDTO::getLadingNo, (key1, key2) -> key2));
        for (OrderBaseInfoDTO orderBaseInfoDTO : orderBaseInfoDTOList) {
            if (orderShippingLadingNoMap.containsKey(orderBaseInfoDTO.getOrderNo())) {
                orderBaseInfoDTO.setLadingNo(orderShippingLadingNoMap.get(orderBaseInfoDTO.getOrderNo()));
            }
        }
    }

    /**
     * 拼接发票名称数据
     *
     * @param orderBaseInfoDTOList
     * @param orderNoList
     */
    private void appendInvoiceTitle(List<OrderBaseInfoDTO> orderBaseInfoDTOList, List<String> orderNoList) {
        List<OrderAttachmentDTO> orderAttachmentDTOList = orderAttachmentDomainService.getAttachmentByOrder(orderNoList, Arrays.asList(OrderAttachmentTypeEnum.OIQ_EXPRESS.getIndex()));

        Map<String, String> orderAttachmentFileNameMap = orderAttachmentDTOList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.groupingBy(OrderAttachmentDTO::getOrderNo),
                        map -> map.entrySet().stream().collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry
                                        .getValue()
                                        .stream()
                                        .map(OrderAttachmentDTO::getFileName)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.joining(";"))))));
        for (OrderBaseInfoDTO orderBaseInfoDTO : orderBaseInfoDTOList) {
            if (!ValidationUtil.isEmpty(orderAttachmentFileNameMap.get(orderBaseInfoDTO.getOrderNo()))) {
                orderBaseInfoDTO.setInvoiceTitle(orderAttachmentFileNameMap.get(orderBaseInfoDTO.getOrderNo()));
            } else {
                orderBaseInfoDTO.setInvoiceTitle("");
            }
        }
    }
}
